name: Run tests

on:
  push:
    branches: [ 'main' ]
  pull_request:
    branches: [ 'main' ]

jobs:
  test:

    runs-on: ubuntu-latest

    strategy:
      matrix:
        # TODO(b/419883265): remove commonjs bundles when dropping node 20 support.
        node-version: [20.x, 22.x, 24.x]

    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    - run: npm ci
    - run: npm run build
    - run: npm run unit-test
    - run: npm run test-server-tests
    - run: |
        cd sdk-samples
        npm install
        npm run build

  api-consistency:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - run: scripts/validate_api_consistency.sh
