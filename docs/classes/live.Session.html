<!DOCTYPE html><html class="default" lang="en" data-base=".."><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>Session | @google/genai</title><meta name="description" content="Documentation for @google/genai"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search"><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@google/genai</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@google/genai</a></li><li><a href="../modules/live.html">live</a></li><li><a href="live.Session.html">Session</a></li></ul><h1>Class Session<code class="tsd-tag">Experimental</code></h1></div><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><p>Represents a connection to the API.</p>
</div><div class="tsd-comment tsd-typography"></div></section><aside class="tsd-sources"><ul><li>Defined in live.ts:209</li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Constructors</h3><div class="tsd-index-list"><a href="live.Session.html#constructor" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Constructor"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Properties</h3><div class="tsd-index-list"><a href="live.Session.html#conn" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>conn</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Methods</h3><div class="tsd-index-list"><a href="live.Session.html#close" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>close</span></a>
<a href="live.Session.html#sendclientcontent" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>send<wbr/>Client<wbr/>Content</span></a>
<a href="live.Session.html#sendrealtimeinput" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>send<wbr/>Realtime<wbr/>Input</span></a>
<a href="live.Session.html#sendtoolresponse" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>send<wbr/>Tool<wbr/>Response</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Constructors"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Constructors</h2></summary><section><section class="tsd-panel tsd-member"><a id="constructor" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>constructor</span><a href="#constructor" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="constructorsession" class="tsd-anchor"></a><span class="tsd-signature-keyword">new</span> <span class="tsd-kind-constructor-signature">Session</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">conn</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">WebSocket</span><span class="tsd-signature-symbol">,</span> <span class="tsd-kind-parameter">apiClient</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">ApiClient</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <a href="live.Session.html" class="tsd-signature-type tsd-kind-class">Session</a><a href="#constructorsession" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><code class="tsd-tag">Experimental</code><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">conn</span>: <span class="tsd-signature-type">WebSocket</span></span></li><li><span><span class="tsd-kind-parameter">apiClient</span>: <span class="tsd-signature-type">ApiClient</span></span></li></ul></div><h4 class="tsd-returns-title">Returns <a href="live.Session.html" class="tsd-signature-type tsd-kind-class">Session</a></h4><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in live.ts:210</li></ul></aside></div></li></ul></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Properties"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Properties</h2></summary><section><section class="tsd-panel tsd-member"><a id="conn" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Readonly</code> <code class="tsd-tag">Experimental</code><span>conn</span><a href="#conn" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">conn</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">WebSocket</span></div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in live.ts:211</li></ul></aside></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Methods"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Methods</h2></summary><section><section class="tsd-panel tsd-member"><a id="close" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>close</span><a href="#close" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="close-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">close</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">void</span><a href="#close-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><code class="tsd-tag">Experimental</code><div class="tsd-comment tsd-typography"><p>Terminates the WebSocket connection.</p>
</div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4><div class="tsd-comment tsd-typography"><div class="tsd-tag-example"><h4 class="tsd-anchor-link"><a id="example" class="tsd-anchor"></a>Example<a href="#example" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><pre><code class="ts"><span class="hl-5">const</span><span class="hl-1"> </span><span class="hl-6">session</span><span class="hl-1"> = </span><span class="hl-3">await</span><span class="hl-1"> </span><span class="hl-4">ai</span><span class="hl-1">.</span><span class="hl-4">live</span><span class="hl-1">.</span><span class="hl-0">connect</span><span class="hl-1">({</span><br/><span class="hl-1">  </span><span class="hl-4">model:</span><span class="hl-1"> </span><span class="hl-2">&#39;gemini-2.0-flash-exp&#39;</span><span class="hl-1">,</span><br/><span class="hl-1">  </span><span class="hl-4">config:</span><span class="hl-1"> {</span><br/><span class="hl-1">    </span><span class="hl-4">responseModalities:</span><span class="hl-1"> [</span><span class="hl-4">Modality</span><span class="hl-1">.</span><span class="hl-6">AUDIO</span><span class="hl-1">],</span><br/><span class="hl-1">  }</span><br/><span class="hl-1">});</span><br/><br/><span class="hl-4">session</span><span class="hl-1">.</span><span class="hl-0">close</span><span class="hl-1">();</span>
</code><button type="button">Copy</button></pre>

</div></div><aside class="tsd-sources"><ul><li>Defined in live.ts:437</li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><a id="sendclientcontent" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>send<wbr/>Client<wbr/>Content</span><a href="#sendclientcontent" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="sendclientcontent-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">sendClientContent</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/types.LiveSendClientContentParameters.html" class="tsd-signature-type tsd-kind-interface">LiveSendClientContentParameters</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">void</span><a href="#sendclientcontent-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><code class="tsd-tag">Experimental</code><div class="tsd-comment tsd-typography"><p>Send a message over the established connection.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">params</span>: <a href="../interfaces/types.LiveSendClientContentParameters.html" class="tsd-signature-type tsd-kind-interface">LiveSendClientContentParameters</a></span><div class="tsd-comment tsd-typography"><p>Contains two <strong>optional</strong> properties, <code>turns</code> and
<code>turnComplete</code>.</p>
<ul>
<li><code>turns</code> will be converted to a <code>Content[]</code></li>
<li><code>turnComplete: true</code> [default] indicates that you are done sending
content and expect a response. If <code>turnComplete: false</code>, the server
will wait for additional messages before starting generation.</li>
</ul>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4><div class="tsd-comment tsd-typography"><div class="tsd-tag-remarks"><h4 class="tsd-anchor-link"><a id="remarks" class="tsd-anchor"></a>Remarks<a href="#remarks" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><p>There are two ways to send messages to the live API:
<code>sendClientContent</code> and <code>sendRealtimeInput</code>.</p>
<p><code>sendClientContent</code> messages are added to the model context <strong>in order</strong>.
Having a conversation using <code>sendClientContent</code> messages is roughly
equivalent to using the <code>Chat.sendMessageStream</code>, except that the state of
the <code>chat</code> history is stored on the API server instead of locally.</p>
<p>Because of <code>sendClientContent</code>'s order guarantee, the model cannot respons
as quickly to <code>sendClientContent</code> messages as to <code>sendRealtimeInput</code>
messages. This makes the biggest difference when sending objects that have
significant preprocessing time (typically images).</p>
<p>The <code>sendClientContent</code> message sends a <code>Content[]</code>
which has more options than the <code>Blob</code> sent by <code>sendRealtimeInput</code>.</p>
<p>So the main use-cases for <code>sendClientContent</code> over <code>sendRealtimeInput</code> are:</p>
<ul>
<li>Sending anything that can't be represented as a <code>Blob</code> (text,
<code>sendClientContent({turns=&quot;Hello?&quot;}</code>)).</li>
<li>Managing turns when not using audio input and voice activity detection.
(<code>sendClientContent({turnComplete:true})</code> or the short form
<code>sendClientContent()</code>)</li>
<li>Prefilling a conversation context<pre><code><span class="hl-0">sendClientContent</span><span class="hl-1">({</span><br/><span class="hl-1">    </span><span class="hl-4">turns:</span><span class="hl-1"> [</span><br/><span class="hl-1">      </span><span class="hl-0">Content</span><span class="hl-1">({</span><span class="hl-4">role:user</span><span class="hl-1">, </span><span class="hl-4">parts:</span><span class="hl-1">...}),</span><br/><span class="hl-1">      </span><span class="hl-0">Content</span><span class="hl-1">({</span><span class="hl-4">role:user</span><span class="hl-1">, </span><span class="hl-4">parts:</span><span class="hl-1">...}),</span><br/><span class="hl-1">      ...</span><br/><span class="hl-1">    ]</span><br/><span class="hl-1">})</span>
</code><button>Copy</button></pre>

</li>
</ul>
</div></div><aside class="tsd-sources"><ul><li>Defined in live.ts:351</li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><a id="sendrealtimeinput" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>send<wbr/>Realtime<wbr/>Input</span><a href="#sendrealtimeinput" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="sendrealtimeinput-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">sendRealtimeInput</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">:</span> <a href="../interfaces/types.LiveSendRealtimeInputParameters.html" class="tsd-signature-type tsd-kind-interface">LiveSendRealtimeInputParameters</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">void</span><a href="#sendrealtimeinput-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><code class="tsd-tag">Experimental</code><div class="tsd-comment tsd-typography"><p>Send a realtime message over the established connection.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">params</span>: <a href="../interfaces/types.LiveSendRealtimeInputParameters.html" class="tsd-signature-type tsd-kind-interface">LiveSendRealtimeInputParameters</a></span><div class="tsd-comment tsd-typography"><p>Contains one property, <code>media</code>.</p>
<ul>
<li><code>media</code> will be converted to a <code>Blob</code></li>
</ul>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4><div class="tsd-comment tsd-typography"><div class="tsd-tag-remarks"><h4 class="tsd-anchor-link"><a id="remarks-1" class="tsd-anchor"></a>Remarks<a href="#remarks-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><p>Use <code>sendRealtimeInput</code> for realtime audio chunks and video frames (images).</p>
<p>With <code>sendRealtimeInput</code> the api will respond to audio automatically
based on voice activity detection (VAD).</p>
<p><code>sendRealtimeInput</code> is optimized for responsivness at the expense of
deterministic ordering guarantees. Audio and video tokens are to the
context when they become available.</p>
<p>Note: The Call signature expects a <code>Blob</code> object, but only a subset
of audio and image mimetypes are allowed.</p>
</div></div><aside class="tsd-sources"><ul><li>Defined in live.ts:386</li></ul></aside></div></li></ul></section><section class="tsd-panel tsd-member"><a id="sendtoolresponse" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>send<wbr/>Tool<wbr/>Response</span><a href="#sendtoolresponse" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="sendtoolresponse-1" class="tsd-anchor"></a><span class="tsd-kind-call-signature">sendToolResponse</span><span class="tsd-signature-symbol">(</span><span class="tsd-kind-parameter">params</span><span class="tsd-signature-symbol">:</span> <a href="types.LiveSendToolResponseParameters.html" class="tsd-signature-type tsd-kind-class">LiveSendToolResponseParameters</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">void</span><a href="#sendtoolresponse-1" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><code class="tsd-tag">Experimental</code><div class="tsd-comment tsd-typography"><p>Send a function response message over the established connection.</p>
</div><div class="tsd-parameters"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameter-list"><li><span><span class="tsd-kind-parameter">params</span>: <a href="types.LiveSendToolResponseParameters.html" class="tsd-signature-type tsd-kind-class">LiveSendToolResponseParameters</a></span><div class="tsd-comment tsd-typography"><p>Contains property <code>functionResponses</code>.</p>
<ul>
<li><code>functionResponses</code> will be converted to a <code>functionResponses[]</code></li>
</ul>
</div><div class="tsd-comment tsd-typography"></div></li></ul></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4><div class="tsd-comment tsd-typography"><div class="tsd-tag-remarks"><h4 class="tsd-anchor-link"><a id="remarks-2" class="tsd-anchor"></a>Remarks<a href="#remarks-2" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h4><p>Use <code>sendFunctionResponse</code> to reply to <code>LiveServerToolCall</code> from the server.</p>
<p>Use <a href="../interfaces/types.LiveConnectConfig.html#tools" class="tsd-kind-property">types.LiveConnectConfig#tools</a> to configure the callable functions.</p>
</div></div><aside class="tsd-sources"><ul><li>Defined in live.ts:410</li></ul></aside></div></li></ul></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Constructors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Constructors</summary><div><a href="#constructor" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Constructor"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Properties</summary><div><a href="#conn" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>conn</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Methods"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Methods</summary><div><a href="#close" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>close</span></a><a href="#sendclientcontent" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>send<wbr/>Client<wbr/>Content</span></a><a href="#sendrealtimeinput" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>send<wbr/>Realtime<wbr/>Input</span></a><a href="#sendtoolresponse" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Method"><use href="../assets/icons.svg#icon-2048"></use></svg><span>send<wbr/>Tool<wbr/>Response</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">@google/genai</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
