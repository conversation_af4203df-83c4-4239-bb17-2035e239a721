<!DOCTYPE html><html class="default" lang="en" data-base=".."><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>GenerateContentResponseUsageMetadata | @google/genai</title><meta name="description" content="Documentation for @google/genai"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/icons.js" id="tsd-icons-script"></script><script async src="../assets/search.js" id="tsd-search-script"></script><script async src="../assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search"><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@google/genai</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@google/genai</a></li><li><a href="../modules/types.html">types</a></li><li><a href="types.GenerateContentResponseUsageMetadata.html">GenerateContentResponseUsageMetadata</a></li></ul><h1>Class GenerateContentResponseUsageMetadata</h1></div><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><p>Usage metadata about response(s).</p>
</div><div class="tsd-comment tsd-typography"></div></section><aside class="tsd-sources"><ul><li>Defined in types.ts:990</li></ul></aside><section class="tsd-panel-group tsd-index-group"><section class="tsd-panel tsd-index-panel"><details class="tsd-index-content tsd-accordion" open><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg> Index</h5></summary><div class="tsd-accordion-details"><section class="tsd-index-section"><h3 class="tsd-index-heading">Constructors</h3><div class="tsd-index-list"><a href="types.GenerateContentResponseUsageMetadata.html#constructor" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Constructor"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a>
</div></section><section class="tsd-index-section"><h3 class="tsd-index-heading">Properties</h3><div class="tsd-index-list"><a href="types.GenerateContentResponseUsageMetadata.html#cachedcontenttokencount" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>cached<wbr/>Content<wbr/>Token<wbr/>Count?</span></a>
<a href="types.GenerateContentResponseUsageMetadata.html#cachetokensdetails" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>cache<wbr/>Tokens<wbr/>Details?</span></a>
<a href="types.GenerateContentResponseUsageMetadata.html#candidatestokencount" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>candidates<wbr/>Token<wbr/>Count?</span></a>
<a href="types.GenerateContentResponseUsageMetadata.html#candidatestokensdetails" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>candidates<wbr/>Tokens<wbr/>Details?</span></a>
<a href="types.GenerateContentResponseUsageMetadata.html#prompttokencount" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>prompt<wbr/>Token<wbr/>Count?</span></a>
<a href="types.GenerateContentResponseUsageMetadata.html#prompttokensdetails" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>prompt<wbr/>Tokens<wbr/>Details?</span></a>
<a href="types.GenerateContentResponseUsageMetadata.html#thoughtstokencount" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>thoughts<wbr/>Token<wbr/>Count?</span></a>
<a href="types.GenerateContentResponseUsageMetadata.html#tooluseprompttokencount" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>tool<wbr/>Use<wbr/>Prompt<wbr/>Token<wbr/>Count?</span></a>
<a href="types.GenerateContentResponseUsageMetadata.html#tooluseprompttokensdetails" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>tool<wbr/>Use<wbr/>Prompt<wbr/>Tokens<wbr/>Details?</span></a>
<a href="types.GenerateContentResponseUsageMetadata.html#totaltokencount" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>total<wbr/>Token<wbr/>Count?</span></a>
</div></section></div></details></section></section><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Constructors"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Constructors</h2></summary><section><section class="tsd-panel tsd-member"><a id="constructor" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><span>constructor</span><a href="#constructor" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><ul class="tsd-signatures"><li class=""><div class="tsd-signature tsd-anchor-link"><a id="constructorgeneratecontentresponseusagemetadata" class="tsd-anchor"></a><span class="tsd-signature-keyword">new</span> <span class="tsd-kind-constructor-signature">GenerateContentResponseUsageMetadata</span><span class="tsd-signature-symbol">()</span><span class="tsd-signature-symbol">:</span> <a href="types.GenerateContentResponseUsageMetadata.html" class="tsd-signature-type tsd-kind-class">GenerateContentResponseUsageMetadata</a><a href="#constructorgeneratecontentresponseusagemetadata" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></div><div class="tsd-description"><h4 class="tsd-returns-title">Returns <a href="types.GenerateContentResponseUsageMetadata.html" class="tsd-signature-type tsd-kind-class">GenerateContentResponseUsageMetadata</a></h4></div></li></ul></section></section></details><details class="tsd-panel-group tsd-member-group tsd-accordion" open><summary class="tsd-accordion-summary" data-key="section-Properties"><h2><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg> Properties</h2></summary><section><section class="tsd-panel tsd-member"><a id="cachedcontenttokencount" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>cached<wbr/>Content<wbr/>Token<wbr/>Count</span><a href="#cachedcontenttokencount" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">cachedContentTokenCount</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Output only. Number of tokens in the cached part in the input (the cached content).</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in types.ts:994</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="cachetokensdetails" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>cache<wbr/>Tokens<wbr/>Details</span><a href="#cachetokensdetails" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">cacheTokensDetails</span><span class="tsd-signature-symbol">?:</span> <a href="../interfaces/types.ModalityTokenCount.html" class="tsd-signature-type tsd-kind-interface">ModalityTokenCount</a><span class="tsd-signature-symbol">[]</span></div><div class="tsd-comment tsd-typography"><p>Output only. List of modalities of the cached content in the request input.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in types.ts:992</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="candidatestokencount" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>candidates<wbr/>Token<wbr/>Count</span><a href="#candidatestokencount" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">candidatesTokenCount</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Number of tokens in the response(s).</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in types.ts:996</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="candidatestokensdetails" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>candidates<wbr/>Tokens<wbr/>Details</span><a href="#candidatestokensdetails" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">candidatesTokensDetails</span><span class="tsd-signature-symbol">?:</span> <a href="../interfaces/types.ModalityTokenCount.html" class="tsd-signature-type tsd-kind-interface">ModalityTokenCount</a><span class="tsd-signature-symbol">[]</span></div><div class="tsd-comment tsd-typography"><p>Output only. List of modalities that were returned in the response.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in types.ts:998</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="prompttokencount" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>prompt<wbr/>Token<wbr/>Count</span><a href="#prompttokencount" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">promptTokenCount</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Number of tokens in the request. When <code>cached_content</code> is set, this is still the total effective prompt size meaning this includes the number of tokens in the cached content.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in types.ts:1000</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="prompttokensdetails" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>prompt<wbr/>Tokens<wbr/>Details</span><a href="#prompttokensdetails" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">promptTokensDetails</span><span class="tsd-signature-symbol">?:</span> <a href="../interfaces/types.ModalityTokenCount.html" class="tsd-signature-type tsd-kind-interface">ModalityTokenCount</a><span class="tsd-signature-symbol">[]</span></div><div class="tsd-comment tsd-typography"><p>Output only. List of modalities that were processed in the request input.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in types.ts:1002</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="thoughtstokencount" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>thoughts<wbr/>Token<wbr/>Count</span><a href="#thoughtstokencount" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">thoughtsTokenCount</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Output only. Number of tokens present in thoughts output.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in types.ts:1004</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="tooluseprompttokencount" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>tool<wbr/>Use<wbr/>Prompt<wbr/>Token<wbr/>Count</span><a href="#tooluseprompttokencount" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">toolUsePromptTokenCount</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Output only. Number of tokens present in tool-use prompt(s).</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in types.ts:1006</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="tooluseprompttokensdetails" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>tool<wbr/>Use<wbr/>Prompt<wbr/>Tokens<wbr/>Details</span><a href="#tooluseprompttokensdetails" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">toolUsePromptTokensDetails</span><span class="tsd-signature-symbol">?:</span> <a href="../interfaces/types.ModalityTokenCount.html" class="tsd-signature-type tsd-kind-interface">ModalityTokenCount</a><span class="tsd-signature-symbol">[]</span></div><div class="tsd-comment tsd-typography"><p>Output only. List of modalities that were processed for tool-use request inputs.</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in types.ts:1008</li></ul></aside></section><section class="tsd-panel tsd-member"><a id="totaltokencount" class="tsd-anchor"></a><h3 class="tsd-anchor-link"><code class="tsd-tag">Optional</code><span>total<wbr/>Token<wbr/>Count</span><a href="#totaltokencount" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24" aria-hidden="true"><use href="../assets/icons.svg#icon-anchor"></use></svg></a></h3><div class="tsd-signature"><span class="tsd-kind-property">totalTokenCount</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><div class="tsd-comment tsd-typography"><p>Total token count for prompt, response candidates, and tool-use prompts (if present).</p>
</div><div class="tsd-comment tsd-typography"></div><aside class="tsd-sources"><ul><li>Defined in types.ts:1010</li></ul></aside></section></section></details></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Constructors"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Constructors</summary><div><a href="#constructor" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Constructor"><use href="../assets/icons.svg#icon-512"></use></svg><span>constructor</span></a></div></details><details open class="tsd-accordion tsd-page-navigation-section"><summary class="tsd-accordion-summary" data-key="section-Properties"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" aria-hidden="true"><use href="../assets/icons.svg#icon-chevronDown"></use></svg>Properties</summary><div><a href="#cachedcontenttokencount" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>cached<wbr/>Content<wbr/>Token<wbr/>Count</span></a><a href="#cachetokensdetails" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>cache<wbr/>Tokens<wbr/>Details</span></a><a href="#candidatestokencount" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>candidates<wbr/>Token<wbr/>Count</span></a><a href="#candidatestokensdetails" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>candidates<wbr/>Tokens<wbr/>Details</span></a><a href="#prompttokencount" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>prompt<wbr/>Token<wbr/>Count</span></a><a href="#prompttokensdetails" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>prompt<wbr/>Tokens<wbr/>Details</span></a><a href="#thoughtstokencount" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>thoughts<wbr/>Token<wbr/>Count</span></a><a href="#tooluseprompttokencount" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>tool<wbr/>Use<wbr/>Prompt<wbr/>Token<wbr/>Count</span></a><a href="#tooluseprompttokensdetails" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>tool<wbr/>Use<wbr/>Prompt<wbr/>Tokens<wbr/>Details</span></a><a href="#totaltokencount" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24" aria-label="Property"><use href="../assets/icons.svg#icon-1024"></use></svg><span>total<wbr/>Token<wbr/>Count</span></a></div></details></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="../modules.html">@google/genai</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
