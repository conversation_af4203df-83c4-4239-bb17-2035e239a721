## API Report File for "@google/genai"

> Do not edit this file. It is a report generated by [API Extractor](https://api-extractor.com/).

```ts

import type { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { GoogleAuthOptions } from 'google-auth-library';

// @public
export interface ActivityEnd {
}

// @public
export enum ActivityHandling {
    ACTIVITY_HANDLING_UNSPECIFIED = "ACTIVITY_HANDLING_UNSPECIFIED",
    NO_INTERRUPTION = "NO_INTERRUPTION",
    START_OF_ACTIVITY_INTERRUPTS = "START_OF_ACTIVITY_INTERRUPTS"
}

// @public
export interface ActivityStart {
}

// @public
export enum AdapterSize {
    ADAPTER_SIZE_EIGHT = "ADAPTER_SIZE_EIGHT",
    ADAPTER_SIZE_FOUR = "ADAPTER_SIZE_FOUR",
    ADAPTER_SIZE_ONE = "ADAPTER_SIZE_ONE",
    ADAPTER_SIZE_SIXTEEN = "ADAPTER_SIZE_SIXTEEN",
    ADAPTER_SIZE_THIRTY_TWO = "ADAPTER_SIZE_THIRTY_TWO",
    ADAPTER_SIZE_TWO = "ADAPTER_SIZE_TWO",
    ADAPTER_SIZE_UNSPECIFIED = "ADAPTER_SIZE_UNSPECIFIED"
}

// @public
export interface ApiAuth {
    apiKeyConfig?: ApiAuthApiKeyConfig;
}

// @public
export interface ApiAuthApiKeyConfig {
    apiKeySecretVersion?: string;
    apiKeyString?: string;
}

// @public
export class ApiError extends Error {
    constructor(options: ApiErrorInfo);
    status: number;
}

// @public
export interface ApiErrorInfo {
    message: string;
    status: number;
}

// @public
export interface ApiKeyConfig {
    apiKeyString?: string;
}

// @public
export enum ApiSpec {
    API_SPEC_UNSPECIFIED = "API_SPEC_UNSPECIFIED",
    ELASTIC_SEARCH = "ELASTIC_SEARCH",
    SIMPLE_SEARCH = "SIMPLE_SEARCH"
}

// @public
export interface AudioChunk {
    data?: string;
    mimeType?: string;
    sourceMetadata?: LiveMusicSourceMetadata;
}

// @public
export interface AudioTranscriptionConfig {
}

// @public
export interface AuthConfig {
    apiKeyConfig?: ApiKeyConfig;
    authType?: AuthType;
    googleServiceAccountConfig?: AuthConfigGoogleServiceAccountConfig;
    httpBasicAuthConfig?: AuthConfigHttpBasicAuthConfig;
    oauthConfig?: AuthConfigOauthConfig;
    oidcConfig?: AuthConfigOidcConfig;
}

// @public
export interface AuthConfigGoogleServiceAccountConfig {
    serviceAccount?: string;
}

// @public
export interface AuthConfigHttpBasicAuthConfig {
    credentialSecret?: string;
}

// @public
export interface AuthConfigOauthConfig {
    accessToken?: string;
    serviceAccount?: string;
}

// @public
export interface AuthConfigOidcConfig {
    idToken?: string;
    serviceAccount?: string;
}

// @public
export interface AuthToken {
    name?: string;
}

// @public
export enum AuthType {
    API_KEY_AUTH = "API_KEY_AUTH",
    // (undocumented)
    AUTH_TYPE_UNSPECIFIED = "AUTH_TYPE_UNSPECIFIED",
    GOOGLE_SERVICE_ACCOUNT_AUTH = "GOOGLE_SERVICE_ACCOUNT_AUTH",
    HTTP_BASIC_AUTH = "HTTP_BASIC_AUTH",
    NO_AUTH = "NO_AUTH",
    OAUTH = "OAUTH",
    OIDC_AUTH = "OIDC_AUTH"
}

// @public
export interface AutomaticActivityDetection {
    disabled?: boolean;
    endOfSpeechSensitivity?: EndSensitivity;
    prefixPaddingMs?: number;
    silenceDurationMs?: number;
    startOfSpeechSensitivity?: StartSensitivity;
}

// @public
export interface AutomaticFunctionCallingConfig {
    disable?: boolean;
    ignoreCallHistory?: boolean;
    maximumRemoteCalls?: number;
}

// @public
export interface BaseUrlParameters {
    // (undocumented)
    geminiUrl?: string;
    // (undocumented)
    vertexUrl?: string;
}

// Warning: (ae-forgotten-export) The symbol "BaseModule" needs to be exported by the entry point index.d.ts
//
// @public (undocumented)
export class Batches extends BaseModule {
    // Warning: (ae-forgotten-export) The symbol "ApiClient" needs to be exported by the entry point index.d.ts
    constructor(apiClient: ApiClient);
    cancel(params: types.CancelBatchJobParameters): Promise<void>;
    // Warning: (ae-forgotten-export) The symbol "types" needs to be exported by the entry point index.d.ts
    create: (params: types.CreateBatchJobParameters) => Promise<types.BatchJob>;
    delete(params: types.DeleteBatchJobParameters): Promise<types.DeleteResourceJob>;
    get(params: types.GetBatchJobParameters): Promise<types.BatchJob>;
    list: (params?: types.ListBatchJobsParameters) => Promise<Pager<types.BatchJob>>;
}

// @public
export interface BatchJob {
    createTime?: string;
    dest?: BatchJobDestination;
    displayName?: string;
    endTime?: string;
    error?: JobError;
    model?: string;
    name?: string;
    src?: BatchJobSource;
    startTime?: string;
    state?: JobState;
    updateTime?: string;
}

// @public
export interface BatchJobDestination {
    bigqueryUri?: string;
    fileName?: string;
    format?: string;
    gcsUri?: string;
    inlinedResponses?: InlinedResponse[];
}

// @public
export interface BatchJobSource {
    bigqueryUri?: string;
    fileName?: string;
    format?: string;
    gcsUri?: string[];
    inlinedRequests?: InlinedRequest[];
}

// @public (undocumented)
export type BatchJobSourceUnion = BatchJobSource | InlinedRequest[] | string;

// @public
export enum Behavior {
    BLOCKING = "BLOCKING",
    NON_BLOCKING = "NON_BLOCKING",
    UNSPECIFIED = "UNSPECIFIED"
}

// @public
interface Blob_2 {
    data?: string;
    displayName?: string;
    mimeType?: string;
}
export { Blob_2 as Blob }

// @public (undocumented)
export type BlobImageUnion = Blob_2;

// @public
export enum BlockedReason {
    BLOCKED_REASON_UNSPECIFIED = "BLOCKED_REASON_UNSPECIFIED",
    BLOCKLIST = "BLOCKLIST",
    IMAGE_SAFETY = "IMAGE_SAFETY",
    OTHER = "OTHER",
    PROHIBITED_CONTENT = "PROHIBITED_CONTENT",
    SAFETY = "SAFETY"
}

// @public
export interface CachedContent {
    createTime?: string;
    displayName?: string;
    expireTime?: string;
    model?: string;
    name?: string;
    updateTime?: string;
    usageMetadata?: CachedContentUsageMetadata;
}

// @public
export interface CachedContentUsageMetadata {
    audioDurationSeconds?: number;
    imageCount?: number;
    textCount?: number;
    totalTokenCount?: number;
    videoDurationSeconds?: number;
}

// @public (undocumented)
export class Caches extends BaseModule {
    constructor(apiClient: ApiClient);
    create(params: types.CreateCachedContentParameters): Promise<types.CachedContent>;
    delete(params: types.DeleteCachedContentParameters): Promise<types.DeleteCachedContentResponse>;
    get(params: types.GetCachedContentParameters): Promise<types.CachedContent>;
    list: (params?: types.ListCachedContentsParameters) => Promise<Pager<types.CachedContent>>;
    update(params: types.UpdateCachedContentParameters): Promise<types.CachedContent>;
}

// @public
export interface CallableTool {
    callTool(functionCalls: FunctionCall[]): Promise<Part[]>;
    tool(): Promise<Tool>;
}

// @public
export interface CallableToolConfig {
    behavior?: Behavior;
    timeout?: number;
}

// @public
export interface CancelBatchJobConfig {
    abortSignal?: AbortSignal;
    httpOptions?: HttpOptions;
}

// @public
export interface CancelBatchJobParameters {
    config?: CancelBatchJobConfig;
    name: string;
}

// @public
export interface Candidate {
    avgLogprobs?: number;
    citationMetadata?: CitationMetadata;
    content?: Content;
    finishMessage?: string;
    finishReason?: FinishReason;
    groundingMetadata?: GroundingMetadata;
    index?: number;
    logprobsResult?: LogprobsResult;
    safetyRatings?: SafetyRating[];
    tokenCount?: number;
    urlContextMetadata?: UrlContextMetadata;
}

// @public
export class Chat {
    constructor(apiClient: ApiClient, modelsModule: Models, model: string, config?: types.GenerateContentConfig, history?: types.Content[]);
    getHistory(curated?: boolean): types.Content[];
    sendMessage(params: types.SendMessageParameters): Promise<types.GenerateContentResponse>;
    sendMessageStream(params: types.SendMessageParameters): Promise<AsyncGenerator<types.GenerateContentResponse>>;
}

// @public
export class Chats {
    constructor(modelsModule: Models, apiClient: ApiClient);
    create(params: types.CreateChatParameters): Chat;
}

// @public
export interface Checkpoint {
    checkpointId?: string;
    epoch?: string;
    step?: string;
}

// @public
export interface Citation {
    endIndex?: number;
    license?: string;
    publicationDate?: GoogleTypeDate;
    startIndex?: number;
    title?: string;
    uri?: string;
}

// @public
export interface CitationMetadata {
    citations?: Citation[];
}

// @public
export interface CodeExecutionResult {
    outcome?: Outcome;
    output?: string;
}

// @public
export interface ComputeTokensConfig {
    abortSignal?: AbortSignal;
    httpOptions?: HttpOptions;
}

// @public
export interface ComputeTokensParameters {
    config?: ComputeTokensConfig;
    contents: ContentListUnion;
    model: string;
}

// @public
export class ComputeTokensResponse {
    tokensInfo?: TokensInfo[];
}

// @public
export interface Content {
    parts?: Part[];
    role?: string;
}

// @public
export interface ContentEmbedding {
    statistics?: ContentEmbeddingStatistics;
    values?: number[];
}

// @public
export interface ContentEmbeddingStatistics {
    tokenCount?: number;
    truncated?: boolean;
}

// @public (undocumented)
export type ContentListUnion = Content | Content[] | PartUnion | PartUnion[];

// @public (undocumented)
export type ContentUnion = Content | PartUnion[] | PartUnion;

// @public
export interface ContextWindowCompressionConfig {
    slidingWindow?: SlidingWindow;
    triggerTokens?: string;
}

// @public
export interface ControlReferenceConfig {
    controlType?: ControlReferenceType;
    enableControlImageComputation?: boolean;
}

// @public
export class ControlReferenceImage {
    config?: ControlReferenceConfig;
    referenceId?: number;
    referenceImage?: Image_2;
    referenceType?: string;
    // Warning: (ae-forgotten-export) The symbol "ReferenceImageAPIInternal" needs to be exported by the entry point index.d.ts
    toReferenceImageAPI(): ReferenceImageAPIInternal;
}

// @public
export enum ControlReferenceType {
    // (undocumented)
    CONTROL_TYPE_CANNY = "CONTROL_TYPE_CANNY",
    // (undocumented)
    CONTROL_TYPE_DEFAULT = "CONTROL_TYPE_DEFAULT",
    // (undocumented)
    CONTROL_TYPE_FACE_MESH = "CONTROL_TYPE_FACE_MESH",
    // (undocumented)
    CONTROL_TYPE_SCRIBBLE = "CONTROL_TYPE_SCRIBBLE"
}

// @public
export interface CountTokensConfig {
    abortSignal?: AbortSignal;
    generationConfig?: GenerationConfig;
    httpOptions?: HttpOptions;
    systemInstruction?: ContentUnion;
    tools?: Tool[];
}

// @public
export interface CountTokensParameters {
    config?: CountTokensConfig;
    contents: ContentListUnion;
    model: string;
}

// @public
export class CountTokensResponse {
    cachedContentTokenCount?: number;
    totalTokens?: number;
}

// @public
export interface CreateAuthTokenConfig {
    abortSignal?: AbortSignal;
    expireTime?: string;
    httpOptions?: HttpOptions;
    liveConnectConstraints?: LiveConnectConstraints;
    lockAdditionalFields?: string[];
    newSessionExpireTime?: string;
    uses?: number;
}

// @public
export interface CreateAuthTokenParameters {
    config?: CreateAuthTokenConfig;
}

// @public
export interface CreateBatchJobConfig {
    abortSignal?: AbortSignal;
    dest?: string;
    displayName?: string;
    httpOptions?: HttpOptions;
}

// @public
export interface CreateBatchJobParameters {
    config?: CreateBatchJobConfig;
    model?: string;
    src: BatchJobSourceUnion;
}

// @public
export interface CreateCachedContentConfig {
    abortSignal?: AbortSignal;
    contents?: ContentListUnion;
    displayName?: string;
    expireTime?: string;
    httpOptions?: HttpOptions;
    kmsKeyName?: string;
    systemInstruction?: ContentUnion;
    toolConfig?: ToolConfig;
    tools?: Tool[];
    ttl?: string;
}

// @public
export interface CreateCachedContentParameters {
    config?: CreateCachedContentConfig;
    model: string;
}

// @public
export interface CreateChatParameters {
    config?: GenerateContentConfig;
    history?: Content[];
    model: string;
}

// @public
export interface CreateFileConfig {
    abortSignal?: AbortSignal;
    httpOptions?: HttpOptions;
}

// @public
export interface CreateFileParameters {
    config?: CreateFileConfig;
    file: File_2;
}

// @public
export class CreateFileResponse {
    sdkHttpResponse?: HttpResponse;
}

// @public
export function createModelContent(partOrString: PartListUnion | string): Content;

// @public
export function createPartFromBase64(data: string, mimeType: string): Part;

// @public
export function createPartFromCodeExecutionResult(outcome: Outcome, output: string): Part;

// @public
export function createPartFromExecutableCode(code: string, language: Language): Part;

// @public
export function createPartFromFunctionCall(name: string, args: Record<string, unknown>): Part;

// @public
export function createPartFromFunctionResponse(id: string, name: string, response: Record<string, unknown>): Part;

// @public
export function createPartFromText(text: string): Part;

// @public
export function createPartFromUri(uri: string, mimeType: string): Part;

// @public
export interface CreateTuningJobConfig {
    abortSignal?: AbortSignal;
    adapterSize?: AdapterSize;
    batchSize?: number;
    description?: string;
    epochCount?: number;
    exportLastCheckpointOnly?: boolean;
    httpOptions?: HttpOptions;
    learningRate?: number;
    learningRateMultiplier?: number;
    tunedModelDisplayName?: string;
    validationDataset?: TuningValidationDataset;
}

// @public
export interface CreateTuningJobParameters {
    baseModel: string;
    config?: CreateTuningJobConfig;
    trainingDataset: TuningDataset;
}

// @public
export function createUserContent(partOrString: PartListUnion | string): Content;

// @public
export interface DatasetDistribution {
    buckets?: DatasetDistributionDistributionBucket[];
    max?: number;
    mean?: number;
    median?: number;
    min?: number;
    p5?: number;
    p95?: number;
    sum?: number;
}

// @public
export interface DatasetDistributionDistributionBucket {
    count?: string;
    left?: number;
    right?: number;
}

// @public
export interface DatasetStats {
    totalBillableCharacterCount?: string;
    totalTuningCharacterCount?: string;
    tuningDatasetExampleCount?: string;
    tuningStepCount?: string;
    userDatasetExamples?: Content[];
    userInputTokenDistribution?: DatasetDistribution;
    userMessagePerExampleDistribution?: DatasetDistribution;
    userOutputTokenDistribution?: DatasetDistribution;
}

// @public
export interface DeleteBatchJobConfig {
    abortSignal?: AbortSignal;
    httpOptions?: HttpOptions;
}

// @public
export interface DeleteBatchJobParameters {
    config?: DeleteBatchJobConfig;
    name: string;
}

// @public
export interface DeleteCachedContentConfig {
    abortSignal?: AbortSignal;
    httpOptions?: HttpOptions;
}

// @public
export interface DeleteCachedContentParameters {
    config?: DeleteCachedContentConfig;
    name: string;
}

// @public
export class DeleteCachedContentResponse {
}

// @public
export interface DeleteFileConfig {
    abortSignal?: AbortSignal;
    httpOptions?: HttpOptions;
}

// @public
export interface DeleteFileParameters {
    config?: DeleteFileConfig;
    name: string;
}

// @public
export class DeleteFileResponse {
}

// @public
export interface DeleteModelConfig {
    abortSignal?: AbortSignal;
    httpOptions?: HttpOptions;
}

// @public
export interface DeleteModelParameters {
    config?: DeleteModelConfig;
    // (undocumented)
    model: string;
}

// @public (undocumented)
export class DeleteModelResponse {
}

// @public
export interface DeleteResourceJob {
    // (undocumented)
    done?: boolean;
    // (undocumented)
    error?: JobError;
    // (undocumented)
    name?: string;
}

// @public
export interface DistillationDataStats {
    trainingDatasetStats?: DatasetStats;
}

// @public
export interface DistillationHyperParameters {
    adapterSize?: AdapterSize;
    epochCount?: string;
    learningRateMultiplier?: number;
}

// @public
export interface DistillationSpec {
    baseTeacherModel?: string;
    hyperParameters?: DistillationHyperParameters;
    pipelineRootDirectory?: string;
    studentModel?: string;
    trainingDatasetUri?: string;
    tunedTeacherModelSource?: string;
    validationDatasetUri?: string;
}

// @public (undocumented)
export type DownloadableFileUnion = string | File_2 | GeneratedVideo | Video;

// @public
export interface DownloadFileConfig {
    abortSignal?: AbortSignal;
    httpOptions?: HttpOptions;
}

// @public
export interface DownloadFileParameters {
    config?: DownloadFileConfig;
    downloadPath: string;
    file: DownloadableFileUnion;
}

// @public
export interface DynamicRetrievalConfig {
    dynamicThreshold?: number;
    mode?: DynamicRetrievalConfigMode;
}

// @public
export enum DynamicRetrievalConfigMode {
    MODE_DYNAMIC = "MODE_DYNAMIC",
    MODE_UNSPECIFIED = "MODE_UNSPECIFIED"
}

// @public
export interface EditImageConfig {
    abortSignal?: AbortSignal;
    aspectRatio?: string;
    baseSteps?: number;
    editMode?: EditMode;
    guidanceScale?: number;
    httpOptions?: HttpOptions;
    includeRaiReason?: boolean;
    includeSafetyAttributes?: boolean;
    language?: ImagePromptLanguage;
    negativePrompt?: string;
    numberOfImages?: number;
    outputCompressionQuality?: number;
    outputGcsUri?: string;
    outputMimeType?: string;
    personGeneration?: PersonGeneration;
    safetyFilterLevel?: SafetyFilterLevel;
    seed?: number;
}

// @public
export interface EditImageParameters {
    config?: EditImageConfig;
    model: string;
    prompt: string;
    referenceImages: ReferenceImage[];
}

// @public
export class EditImageResponse {
    generatedImages?: GeneratedImage[];
}

// @public
export enum EditMode {
    // (undocumented)
    EDIT_MODE_BGSWAP = "EDIT_MODE_BGSWAP",
    // (undocumented)
    EDIT_MODE_CONTROLLED_EDITING = "EDIT_MODE_CONTROLLED_EDITING",
    // (undocumented)
    EDIT_MODE_DEFAULT = "EDIT_MODE_DEFAULT",
    // (undocumented)
    EDIT_MODE_INPAINT_INSERTION = "EDIT_MODE_INPAINT_INSERTION",
    // (undocumented)
    EDIT_MODE_INPAINT_REMOVAL = "EDIT_MODE_INPAINT_REMOVAL",
    // (undocumented)
    EDIT_MODE_OUTPAINT = "EDIT_MODE_OUTPAINT",
    // (undocumented)
    EDIT_MODE_PRODUCT_IMAGE = "EDIT_MODE_PRODUCT_IMAGE",
    // (undocumented)
    EDIT_MODE_STYLE = "EDIT_MODE_STYLE"
}

// @public
export interface EmbedContentConfig {
    abortSignal?: AbortSignal;
    autoTruncate?: boolean;
    httpOptions?: HttpOptions;
    mimeType?: string;
    outputDimensionality?: number;
    taskType?: string;
    title?: string;
}

// @public
export interface EmbedContentMetadata {
    billableCharacterCount?: number;
}

// @public
export interface EmbedContentParameters {
    config?: EmbedContentConfig;
    contents: ContentListUnion;
    model: string;
}

// @public
export class EmbedContentResponse {
    embeddings?: ContentEmbedding[];
    metadata?: EmbedContentMetadata;
}

// @public
export interface EncryptionSpec {
    kmsKeyName?: string;
}

// @public
export interface Endpoint {
    deployedModelId?: string;
    name?: string;
}

// @public
export enum EndSensitivity {
    END_SENSITIVITY_HIGH = "END_SENSITIVITY_HIGH",
    END_SENSITIVITY_LOW = "END_SENSITIVITY_LOW",
    END_SENSITIVITY_UNSPECIFIED = "END_SENSITIVITY_UNSPECIFIED"
}

// @public
export interface EnterpriseWebSearch {
}

// @public
export enum Environment {
    ENVIRONMENT_BROWSER = "ENVIRONMENT_BROWSER",
    ENVIRONMENT_UNSPECIFIED = "ENVIRONMENT_UNSPECIFIED"
}

// @public
export interface ExecutableCode {
    code?: string;
    language?: Language;
}

// @public
export interface ExternalApi {
    apiAuth?: ApiAuth;
    apiSpec?: ApiSpec;
    authConfig?: AuthConfig;
    elasticSearchParams?: ExternalApiElasticSearchParams;
    endpoint?: string;
    simpleSearchParams?: ExternalApiSimpleSearchParams;
}

// @public
export interface ExternalApiElasticSearchParams {
    index?: string;
    numHits?: number;
    searchTemplate?: string;
}

// @public
export interface ExternalApiSimpleSearchParams {
}

// @public
export enum FeatureSelectionPreference {
    // (undocumented)
    BALANCED = "BALANCED",
    // (undocumented)
    FEATURE_SELECTION_PREFERENCE_UNSPECIFIED = "FEATURE_SELECTION_PREFERENCE_UNSPECIFIED",
    // (undocumented)
    PRIORITIZE_COST = "PRIORITIZE_COST",
    // (undocumented)
    PRIORITIZE_QUALITY = "PRIORITIZE_QUALITY"
}

// @public (undocumented)
export interface FetchPredictOperationConfig {
    abortSignal?: AbortSignal;
    httpOptions?: HttpOptions;
}

// @public
export interface FetchPredictOperationParameters {
    config?: FetchPredictOperationConfig;
    operationName: string;
    // (undocumented)
    resourceName: string;
}

// @public
interface File_2 {
    createTime?: string;
    displayName?: string;
    downloadUri?: string;
    error?: FileStatus;
    expirationTime?: string;
    mimeType?: string;
    name?: string;
    sha256Hash?: string;
    sizeBytes?: string;
    source?: FileSource;
    state?: FileState;
    updateTime?: string;
    uri?: string;
    videoMetadata?: Record<string, unknown>;
}
export { File_2 as File }

// @public
export interface FileData {
    displayName?: string;
    fileUri?: string;
    mimeType?: string;
}

// @public (undocumented)
export class Files extends BaseModule {
    constructor(apiClient: ApiClient);
    delete(params: types.DeleteFileParameters): Promise<types.DeleteFileResponse>;
    download(params: types.DownloadFileParameters): Promise<void>;
    get(params: types.GetFileParameters): Promise<types.File>;
    list: (params?: types.ListFilesParameters) => Promise<Pager<types.File>>;
    upload(params: types.UploadFileParameters): Promise<types.File>;
}

// @public
export enum FileSource {
    // (undocumented)
    GENERATED = "GENERATED",
    // (undocumented)
    SOURCE_UNSPECIFIED = "SOURCE_UNSPECIFIED",
    // (undocumented)
    UPLOADED = "UPLOADED"
}

// @public
export enum FileState {
    // (undocumented)
    ACTIVE = "ACTIVE",
    // (undocumented)
    FAILED = "FAILED",
    // (undocumented)
    PROCESSING = "PROCESSING",
    // (undocumented)
    STATE_UNSPECIFIED = "STATE_UNSPECIFIED"
}

// @public
export interface FileStatus {
    code?: number;
    details?: Record<string, unknown>[];
    message?: string;
}

// @public
export enum FinishReason {
    BLOCKLIST = "BLOCKLIST",
    FINISH_REASON_UNSPECIFIED = "FINISH_REASON_UNSPECIFIED",
    IMAGE_SAFETY = "IMAGE_SAFETY",
    LANGUAGE = "LANGUAGE",
    MALFORMED_FUNCTION_CALL = "MALFORMED_FUNCTION_CALL",
    MAX_TOKENS = "MAX_TOKENS",
    OTHER = "OTHER",
    PROHIBITED_CONTENT = "PROHIBITED_CONTENT",
    RECITATION = "RECITATION",
    SAFETY = "SAFETY",
    SPII = "SPII",
    STOP = "STOP",
    UNEXPECTED_TOOL_CALL = "UNEXPECTED_TOOL_CALL"
}

// @public
export interface FunctionCall {
    args?: Record<string, unknown>;
    id?: string;
    name?: string;
}

// @public
export interface FunctionCallingConfig {
    allowedFunctionNames?: string[];
    mode?: FunctionCallingConfigMode;
}

// @public
export enum FunctionCallingConfigMode {
    ANY = "ANY",
    AUTO = "AUTO",
    MODE_UNSPECIFIED = "MODE_UNSPECIFIED",
    NONE = "NONE"
}

// @public
export interface FunctionDeclaration {
    behavior?: Behavior;
    description?: string;
    name?: string;
    parameters?: Schema;
    parametersJsonSchema?: unknown;
    response?: Schema;
    responseJsonSchema?: unknown;
}

// @public
export class FunctionResponse {
    id?: string;
    name?: string;
    response?: Record<string, unknown>;
    scheduling?: FunctionResponseScheduling;
    willContinue?: boolean;
}

// @public
export enum FunctionResponseScheduling {
    INTERRUPT = "INTERRUPT",
    SCHEDULING_UNSPECIFIED = "SCHEDULING_UNSPECIFIED",
    SILENT = "SILENT",
    WHEN_IDLE = "WHEN_IDLE"
}

// @public
export interface GenerateContentConfig {
    abortSignal?: AbortSignal;
    audioTimestamp?: boolean;
    automaticFunctionCalling?: AutomaticFunctionCallingConfig;
    cachedContent?: string;
    candidateCount?: number;
    frequencyPenalty?: number;
    httpOptions?: HttpOptions;
    labels?: Record<string, string>;
    logprobs?: number;
    maxOutputTokens?: number;
    mediaResolution?: MediaResolution;
    modelSelectionConfig?: ModelSelectionConfig;
    presencePenalty?: number;
    responseJsonSchema?: unknown;
    responseLogprobs?: boolean;
    responseMimeType?: string;
    responseModalities?: string[];
    responseSchema?: SchemaUnion;
    routingConfig?: GenerationConfigRoutingConfig;
    safetySettings?: SafetySetting[];
    seed?: number;
    speechConfig?: SpeechConfigUnion;
    stopSequences?: string[];
    systemInstruction?: ContentUnion;
    temperature?: number;
    thinkingConfig?: ThinkingConfig;
    toolConfig?: ToolConfig;
    tools?: ToolListUnion;
    topK?: number;
    topP?: number;
}

// @public
export interface GenerateContentParameters {
    config?: GenerateContentConfig;
    contents: ContentListUnion;
    model: string;
}

// @public
export class GenerateContentResponse {
    automaticFunctionCallingHistory?: Content[];
    candidates?: Candidate[];
    get codeExecutionResult(): string | undefined;
    createTime?: string;
    get data(): string | undefined;
    get executableCode(): string | undefined;
    get functionCalls(): FunctionCall[] | undefined;
    modelVersion?: string;
    promptFeedback?: GenerateContentResponsePromptFeedback;
    responseId?: string;
    get text(): string | undefined;
    usageMetadata?: GenerateContentResponseUsageMetadata;
}

// @public
export class GenerateContentResponsePromptFeedback {
    blockReason?: BlockedReason;
    blockReasonMessage?: string;
    safetyRatings?: SafetyRating[];
}

// @public
export class GenerateContentResponseUsageMetadata {
    cachedContentTokenCount?: number;
    cacheTokensDetails?: ModalityTokenCount[];
    candidatesTokenCount?: number;
    candidatesTokensDetails?: ModalityTokenCount[];
    promptTokenCount?: number;
    promptTokensDetails?: ModalityTokenCount[];
    thoughtsTokenCount?: number;
    toolUsePromptTokenCount?: number;
    toolUsePromptTokensDetails?: ModalityTokenCount[];
    totalTokenCount?: number;
    trafficType?: TrafficType;
}

// @public
export interface GeneratedImage {
    enhancedPrompt?: string;
    image?: Image_2;
    raiFilteredReason?: string;
    safetyAttributes?: SafetyAttributes;
}

// @public
export interface GeneratedVideo {
    video?: Video;
}

// @public
export interface GenerateImagesConfig {
    abortSignal?: AbortSignal;
    addWatermark?: boolean;
    aspectRatio?: string;
    enhancePrompt?: boolean;
    guidanceScale?: number;
    httpOptions?: HttpOptions;
    includeRaiReason?: boolean;
    includeSafetyAttributes?: boolean;
    language?: ImagePromptLanguage;
    negativePrompt?: string;
    numberOfImages?: number;
    outputCompressionQuality?: number;
    outputGcsUri?: string;
    outputMimeType?: string;
    personGeneration?: PersonGeneration;
    safetyFilterLevel?: SafetyFilterLevel;
    seed?: number;
}

// @public
export interface GenerateImagesParameters {
    config?: GenerateImagesConfig;
    model: string;
    prompt: string;
}

// @public
export class GenerateImagesResponse {
    generatedImages?: GeneratedImage[];
    positivePromptSafetyAttributes?: SafetyAttributes;
}

// @public
export interface GenerateVideosConfig {
    abortSignal?: AbortSignal;
    aspectRatio?: string;
    compressionQuality?: VideoCompressionQuality;
    durationSeconds?: number;
    enhancePrompt?: boolean;
    fps?: number;
    generateAudio?: boolean;
    httpOptions?: HttpOptions;
    lastFrame?: Image_2;
    negativePrompt?: string;
    numberOfVideos?: number;
    outputGcsUri?: string;
    personGeneration?: string;
    pubsubTopic?: string;
    resolution?: string;
    seed?: number;
}

// @public
export interface GenerateVideosOperation {
    done?: boolean;
    error?: Record<string, unknown>;
    metadata?: Record<string, unknown>;
    name?: string;
    response?: GenerateVideosResponse;
}

// @public
export interface GenerateVideosParameters {
    config?: GenerateVideosConfig;
    image?: Image_2;
    model: string;
    prompt?: string;
    video?: Video;
}

// @public
export class GenerateVideosResponse {
    generatedVideos?: GeneratedVideo[];
    raiMediaFilteredCount?: number;
    raiMediaFilteredReasons?: string[];
}

// @public
export interface GenerationConfig {
    audioTimestamp?: boolean;
    candidateCount?: number;
    enableAffectiveDialog?: boolean;
    frequencyPenalty?: number;
    logprobs?: number;
    maxOutputTokens?: number;
    mediaResolution?: MediaResolution;
    modelSelectionConfig?: ModelSelectionConfig;
    presencePenalty?: number;
    responseJsonSchema?: unknown;
    responseLogprobs?: boolean;
    responseMimeType?: string;
    responseModalities?: Modality[];
    responseSchema?: Schema;
    routingConfig?: GenerationConfigRoutingConfig;
    seed?: number;
    speechConfig?: SpeechConfig;
    stopSequences?: string[];
    temperature?: number;
    thinkingConfig?: GenerationConfigThinkingConfig;
    topK?: number;
    topP?: number;
}

// @public
export interface GenerationConfigRoutingConfig {
    autoMode?: GenerationConfigRoutingConfigAutoRoutingMode;
    manualMode?: GenerationConfigRoutingConfigManualRoutingMode;
}

// @public
export interface GenerationConfigRoutingConfigAutoRoutingMode {
    modelRoutingPreference?: 'UNKNOWN' | 'PRIORITIZE_QUALITY' | 'BALANCED' | 'PRIORITIZE_COST';
}

// @public
export interface GenerationConfigRoutingConfigManualRoutingMode {
    modelName?: string;
}

// @public
export interface GenerationConfigThinkingConfig {
    includeThoughts?: boolean;
    thinkingBudget?: number;
}

// @public
export interface GetBatchJobConfig {
    abortSignal?: AbortSignal;
    httpOptions?: HttpOptions;
}

// @public
export interface GetBatchJobParameters {
    config?: GetBatchJobConfig;
    name: string;
}

// @public
export interface GetCachedContentConfig {
    abortSignal?: AbortSignal;
    httpOptions?: HttpOptions;
}

// @public
export interface GetCachedContentParameters {
    config?: GetCachedContentConfig;
    name: string;
}

// @public
export interface GetFileConfig {
    abortSignal?: AbortSignal;
    httpOptions?: HttpOptions;
}

// @public
export interface GetFileParameters {
    config?: GetFileConfig;
    name: string;
}

// @public
export interface GetModelConfig {
    abortSignal?: AbortSignal;
    httpOptions?: HttpOptions;
}

// @public (undocumented)
export interface GetModelParameters {
    config?: GetModelConfig;
    // (undocumented)
    model: string;
}

// @public (undocumented)
export interface GetOperationConfig {
    abortSignal?: AbortSignal;
    httpOptions?: HttpOptions;
}

// @public
export interface GetOperationParameters {
    config?: GetOperationConfig;
    operationName: string;
}

// @public
export interface GetTuningJobConfig {
    abortSignal?: AbortSignal;
    httpOptions?: HttpOptions;
}

// @public
export interface GetTuningJobParameters {
    config?: GetTuningJobConfig;
    // (undocumented)
    name: string;
}

// @public
export class GoogleGenAI {
    constructor(options: GoogleGenAIOptions);
    // (undocumented)
    protected readonly apiClient: ApiClient;
    // (undocumented)
    readonly authTokens: Tokens;
    // (undocumented)
    readonly batches: Batches;
    // (undocumented)
    readonly caches: Caches;
    // (undocumented)
    readonly chats: Chats;
    // (undocumented)
    readonly files: Files;
    // (undocumented)
    readonly live: Live;
    // (undocumented)
    readonly models: Models;
    // (undocumented)
    readonly operations: Operations;
    // Warning: (ae-forgotten-export) The symbol "Tunings" needs to be exported by the entry point index.d.ts
    //
    // (undocumented)
    readonly tunings: Tunings;
    // (undocumented)
    readonly vertexai: boolean;
}

// @public
export interface GoogleGenAIOptions {
    apiKey?: string;
    apiVersion?: string;
    googleAuthOptions?: GoogleAuthOptions;
    httpOptions?: HttpOptions;
    location?: string;
    project?: string;
    vertexai?: boolean;
}

// @public
export interface GoogleMaps {
    authConfig?: AuthConfig;
}

// @public
export interface GoogleRpcStatus {
    code?: number;
    details?: Record<string, unknown>[];
    message?: string;
}

// @public
export interface GoogleSearch {
    timeRangeFilter?: Interval;
}

// @public
export interface GoogleSearchRetrieval {
    dynamicRetrievalConfig?: DynamicRetrievalConfig;
}

// @public
export interface GoogleTypeDate {
    day?: number;
    month?: number;
    year?: number;
}

// @public
export interface GroundingChunk {
    retrievedContext?: GroundingChunkRetrievedContext;
    web?: GroundingChunkWeb;
}

// @public
export interface GroundingChunkRetrievedContext {
    ragChunk?: RagChunk;
    text?: string;
    title?: string;
    uri?: string;
}

// @public
export interface GroundingChunkWeb {
    domain?: string;
    title?: string;
    uri?: string;
}

// @public
export interface GroundingMetadata {
    groundingChunks?: GroundingChunk[];
    groundingSupports?: GroundingSupport[];
    retrievalMetadata?: RetrievalMetadata;
    retrievalQueries?: string[];
    searchEntryPoint?: SearchEntryPoint;
    webSearchQueries?: string[];
}

// @public
export interface GroundingSupport {
    confidenceScores?: number[];
    groundingChunkIndices?: number[];
    segment?: Segment;
}

// @public
export enum HarmBlockMethod {
    HARM_BLOCK_METHOD_UNSPECIFIED = "HARM_BLOCK_METHOD_UNSPECIFIED",
    PROBABILITY = "PROBABILITY",
    SEVERITY = "SEVERITY"
}

// @public
export enum HarmBlockThreshold {
    BLOCK_LOW_AND_ABOVE = "BLOCK_LOW_AND_ABOVE",
    BLOCK_MEDIUM_AND_ABOVE = "BLOCK_MEDIUM_AND_ABOVE",
    BLOCK_NONE = "BLOCK_NONE",
    BLOCK_ONLY_HIGH = "BLOCK_ONLY_HIGH",
    HARM_BLOCK_THRESHOLD_UNSPECIFIED = "HARM_BLOCK_THRESHOLD_UNSPECIFIED",
    OFF = "OFF"
}

// @public
export enum HarmCategory {
    HARM_CATEGORY_CIVIC_INTEGRITY = "HARM_CATEGORY_CIVIC_INTEGRITY",
    HARM_CATEGORY_DANGEROUS_CONTENT = "HARM_CATEGORY_DANGEROUS_CONTENT",
    HARM_CATEGORY_HARASSMENT = "HARM_CATEGORY_HARASSMENT",
    HARM_CATEGORY_HATE_SPEECH = "HARM_CATEGORY_HATE_SPEECH",
    HARM_CATEGORY_IMAGE_DANGEROUS_CONTENT = "HARM_CATEGORY_IMAGE_DANGEROUS_CONTENT",
    HARM_CATEGORY_IMAGE_HARASSMENT = "HARM_CATEGORY_IMAGE_HARASSMENT",
    HARM_CATEGORY_IMAGE_HATE = "HARM_CATEGORY_IMAGE_HATE",
    HARM_CATEGORY_IMAGE_SEXUALLY_EXPLICIT = "HARM_CATEGORY_IMAGE_SEXUALLY_EXPLICIT",
    HARM_CATEGORY_SEXUALLY_EXPLICIT = "HARM_CATEGORY_SEXUALLY_EXPLICIT",
    HARM_CATEGORY_UNSPECIFIED = "HARM_CATEGORY_UNSPECIFIED"
}

// @public
export enum HarmProbability {
    HARM_PROBABILITY_UNSPECIFIED = "HARM_PROBABILITY_UNSPECIFIED",
    HIGH = "HIGH",
    LOW = "LOW",
    MEDIUM = "MEDIUM",
    NEGLIGIBLE = "NEGLIGIBLE"
}

// @public
export enum HarmSeverity {
    HARM_SEVERITY_HIGH = "HARM_SEVERITY_HIGH",
    HARM_SEVERITY_LOW = "HARM_SEVERITY_LOW",
    HARM_SEVERITY_MEDIUM = "HARM_SEVERITY_MEDIUM",
    HARM_SEVERITY_NEGLIGIBLE = "HARM_SEVERITY_NEGLIGIBLE",
    HARM_SEVERITY_UNSPECIFIED = "HARM_SEVERITY_UNSPECIFIED"
}

// @public
export interface HttpOptions {
    apiVersion?: string;
    baseUrl?: string;
    extraBody?: Record<string, unknown>;
    headers?: Record<string, string>;
    timeout?: number;
}

// @public
export class HttpResponse {
    constructor(response: Response);
    headers?: Record<string, string>;
    // (undocumented)
    json(): Promise<unknown>;
    responseInternal: Response;
}

// @public
interface Image_2 {
    gcsUri?: string;
    imageBytes?: string;
    mimeType?: string;
}
export { Image_2 as Image }

// @public
export enum ImagePromptLanguage {
    auto = "auto",
    en = "en",
    es = "es",
    hi = "hi",
    ja = "ja",
    ko = "ko",
    pt = "pt",
    zh = "zh"
}

// @public
export interface InlinedRequest {
    config?: GenerateContentConfig;
    contents?: ContentListUnion;
    model?: string;
}

// @public
export class InlinedResponse {
    error?: JobError;
    response?: GenerateContentResponse;
}

// @public
export interface Interval {
    endTime?: string;
    startTime?: string;
}

// @public
export interface JobError {
    code?: number;
    details?: string[];
    message?: string;
}

// @public
export enum JobState {
    JOB_STATE_CANCELLED = "JOB_STATE_CANCELLED",
    JOB_STATE_CANCELLING = "JOB_STATE_CANCELLING",
    JOB_STATE_EXPIRED = "JOB_STATE_EXPIRED",
    JOB_STATE_FAILED = "JOB_STATE_FAILED",
    JOB_STATE_PARTIALLY_SUCCEEDED = "JOB_STATE_PARTIALLY_SUCCEEDED",
    JOB_STATE_PAUSED = "JOB_STATE_PAUSED",
    JOB_STATE_PENDING = "JOB_STATE_PENDING",
    JOB_STATE_QUEUED = "JOB_STATE_QUEUED",
    JOB_STATE_RUNNING = "JOB_STATE_RUNNING",
    JOB_STATE_SUCCEEDED = "JOB_STATE_SUCCEEDED",
    JOB_STATE_UNSPECIFIED = "JOB_STATE_UNSPECIFIED",
    JOB_STATE_UPDATING = "JOB_STATE_UPDATING"
}

// @public
export enum Language {
    LANGUAGE_UNSPECIFIED = "LANGUAGE_UNSPECIFIED",
    PYTHON = "PYTHON"
}

// @public
export interface LatLng {
    latitude?: number;
    longitude?: number;
}

// @public
export interface ListBatchJobsConfig {
    abortSignal?: AbortSignal;
    // (undocumented)
    filter?: string;
    httpOptions?: HttpOptions;
    // (undocumented)
    pageSize?: number;
    // (undocumented)
    pageToken?: string;
}

// @public
export interface ListBatchJobsParameters {
    // (undocumented)
    config?: ListBatchJobsConfig;
}

// @public
export class ListBatchJobsResponse {
    // (undocumented)
    batchJobs?: BatchJob[];
    // (undocumented)
    nextPageToken?: string;
}

// @public
export interface ListCachedContentsConfig {
    abortSignal?: AbortSignal;
    httpOptions?: HttpOptions;
    // (undocumented)
    pageSize?: number;
    // (undocumented)
    pageToken?: string;
}

// @public
export interface ListCachedContentsParameters {
    config?: ListCachedContentsConfig;
}

// @public (undocumented)
export class ListCachedContentsResponse {
    cachedContents?: CachedContent[];
    // (undocumented)
    nextPageToken?: string;
}

// @public
export interface ListFilesConfig {
    abortSignal?: AbortSignal;
    httpOptions?: HttpOptions;
    // (undocumented)
    pageSize?: number;
    // (undocumented)
    pageToken?: string;
}

// @public
export interface ListFilesParameters {
    config?: ListFilesConfig;
}

// @public
export class ListFilesResponse {
    files?: File_2[];
    nextPageToken?: string;
}

// @public (undocumented)
export interface ListModelsConfig {
    abortSignal?: AbortSignal;
    // (undocumented)
    filter?: string;
    httpOptions?: HttpOptions;
    // (undocumented)
    pageSize?: number;
    // (undocumented)
    pageToken?: string;
    queryBase?: boolean;
}

// @public (undocumented)
export interface ListModelsParameters {
    // (undocumented)
    config?: ListModelsConfig;
}

// @public (undocumented)
export class ListModelsResponse {
    // (undocumented)
    models?: Model[];
    // (undocumented)
    nextPageToken?: string;
}

// @public
export interface ListTuningJobsConfig {
    abortSignal?: AbortSignal;
    // (undocumented)
    filter?: string;
    httpOptions?: HttpOptions;
    // (undocumented)
    pageSize?: number;
    // (undocumented)
    pageToken?: string;
}

// @public
export interface ListTuningJobsParameters {
    // (undocumented)
    config?: ListTuningJobsConfig;
}

// @public
export class ListTuningJobsResponse {
    nextPageToken?: string;
    tuningJobs?: TuningJob[];
}

// @public
export class Live {
    // Warning: (ae-forgotten-export) The symbol "Auth" needs to be exported by the entry point index.d.ts
    // Warning: (ae-forgotten-export) The symbol "WebSocketFactory" needs to be exported by the entry point index.d.ts
    constructor(apiClient: ApiClient, auth: Auth, webSocketFactory: WebSocketFactory);
    connect(params: types.LiveConnectParameters): Promise<Session>;
    // Warning: (ae-forgotten-export) The symbol "LiveMusic" needs to be exported by the entry point index.d.ts
    //
    // (undocumented)
    readonly music: LiveMusic;
}

// @public
export interface LiveCallbacks {
    onclose?: ((e: CloseEvent) => void) | null;
    onerror?: ((e: ErrorEvent) => void) | null;
    onmessage: (e: LiveServerMessage) => void;
    onopen?: (() => void) | null;
}

// @public
export interface LiveClientContent {
    turnComplete?: boolean;
    turns?: Content[];
}

// @public
export interface LiveClientMessage {
    clientContent?: LiveClientContent;
    realtimeInput?: LiveClientRealtimeInput;
    setup?: LiveClientSetup;
    toolResponse?: LiveClientToolResponse;
}

// @public
export interface LiveClientRealtimeInput {
    activityEnd?: ActivityEnd;
    activityStart?: ActivityStart;
    audio?: Blob_2;
    audioStreamEnd?: boolean;
    mediaChunks?: Blob_2[];
    text?: string;
    video?: Blob_2;
}

// @public
export interface LiveClientSetup {
    contextWindowCompression?: ContextWindowCompressionConfig;
    generationConfig?: GenerationConfig;
    inputAudioTranscription?: AudioTranscriptionConfig;
    model?: string;
    outputAudioTranscription?: AudioTranscriptionConfig;
    proactivity?: ProactivityConfig;
    realtimeInputConfig?: RealtimeInputConfig;
    sessionResumption?: SessionResumptionConfig;
    systemInstruction?: ContentUnion;
    tools?: ToolListUnion;
}

// @public
export class LiveClientToolResponse {
    functionResponses?: FunctionResponse[];
}

// @public
export interface LiveConnectConfig {
    abortSignal?: AbortSignal;
    contextWindowCompression?: ContextWindowCompressionConfig;
    enableAffectiveDialog?: boolean;
    generationConfig?: GenerationConfig;
    httpOptions?: HttpOptions;
    inputAudioTranscription?: AudioTranscriptionConfig;
    maxOutputTokens?: number;
    mediaResolution?: MediaResolution;
    outputAudioTranscription?: AudioTranscriptionConfig;
    proactivity?: ProactivityConfig;
    realtimeInputConfig?: RealtimeInputConfig;
    responseModalities?: Modality[];
    seed?: number;
    sessionResumption?: SessionResumptionConfig;
    speechConfig?: SpeechConfig;
    systemInstruction?: ContentUnion;
    temperature?: number;
    tools?: ToolListUnion;
    topK?: number;
    topP?: number;
}

// @public
export interface LiveConnectConstraints {
    config?: LiveConnectConfig;
    model?: string;
}

// @public
export interface LiveConnectParameters {
    callbacks: LiveCallbacks;
    config?: LiveConnectConfig;
    model: string;
}

// @public
export interface LiveMusicCallbacks {
    onclose?: ((e: CloseEvent) => void) | null;
    onerror?: ((e: ErrorEvent) => void) | null;
    onmessage: (e: LiveMusicServerMessage) => void;
}

// @public
export interface LiveMusicClientContent {
    weightedPrompts?: WeightedPrompt[];
}

// @public
export interface LiveMusicClientMessage {
    clientContent?: LiveMusicClientContent;
    musicGenerationConfig?: LiveMusicGenerationConfig;
    playbackControl?: LiveMusicPlaybackControl;
    setup?: LiveMusicClientSetup;
}

// @public
export interface LiveMusicClientSetup {
    model?: string;
}

// @public
export interface LiveMusicConnectParameters {
    callbacks: LiveMusicCallbacks;
    model: string;
}

// @public
export interface LiveMusicFilteredPrompt {
    filteredReason?: string;
    text?: string;
}

// @public
export interface LiveMusicGenerationConfig {
    bpm?: number;
    brightness?: number;
    density?: number;
    guidance?: number;
    muteBass?: boolean;
    muteDrums?: boolean;
    onlyBassAndDrums?: boolean;
    scale?: Scale;
    seed?: number;
    temperature?: number;
    topK?: number;
}

// @public
export enum LiveMusicPlaybackControl {
    PAUSE = "PAUSE",
    PLAY = "PLAY",
    PLAYBACK_CONTROL_UNSPECIFIED = "PLAYBACK_CONTROL_UNSPECIFIED",
    RESET_CONTEXT = "RESET_CONTEXT",
    STOP = "STOP"
}

// @public
export interface LiveMusicServerContent {
    audioChunks?: AudioChunk[];
}

// @public
export class LiveMusicServerMessage {
    get audioChunk(): AudioChunk | undefined;
    filteredPrompt?: LiveMusicFilteredPrompt;
    serverContent?: LiveMusicServerContent;
    setupComplete?: LiveMusicServerSetupComplete;
}

// @public
export interface LiveMusicServerSetupComplete {
}

// @public
export class LiveMusicSession {
    constructor(conn: WebSocket_2, apiClient: ApiClient);
    close(): void;
    // Warning: (ae-forgotten-export) The symbol "WebSocket_2" needs to be exported by the entry point index.d.ts
    //
    // (undocumented)
    readonly conn: WebSocket_2;
    pause(): void;
    play(): void;
    resetContext(): void;
    setMusicGenerationConfig(params: types.LiveMusicSetConfigParameters): Promise<void>;
    setWeightedPrompts(params: types.LiveMusicSetWeightedPromptsParameters): Promise<void>;
    stop(): void;
}

// @public
export interface LiveMusicSetConfigParameters {
    musicGenerationConfig: LiveMusicGenerationConfig;
}

// @public
export interface LiveMusicSetWeightedPromptsParameters {
    weightedPrompts: WeightedPrompt[];
}

// @public
export interface LiveMusicSourceMetadata {
    clientContent?: LiveMusicClientContent;
    musicGenerationConfig?: LiveMusicGenerationConfig;
}

// @public
export interface LiveSendClientContentParameters {
    turnComplete?: boolean;
    turns?: ContentListUnion;
}

// @public
export interface LiveSendRealtimeInputParameters {
    activityEnd?: ActivityEnd;
    activityStart?: ActivityStart;
    audio?: Blob_2;
    audioStreamEnd?: boolean;
    media?: BlobImageUnion;
    text?: string;
    video?: BlobImageUnion;
}

// @public
export class LiveSendToolResponseParameters {
    functionResponses: FunctionResponse[] | FunctionResponse;
}

// @public
export interface LiveServerContent {
    generationComplete?: boolean;
    groundingMetadata?: GroundingMetadata;
    inputTranscription?: Transcription;
    interrupted?: boolean;
    modelTurn?: Content;
    outputTranscription?: Transcription;
    turnComplete?: boolean;
    urlContextMetadata?: UrlContextMetadata;
}

// @public
export interface LiveServerGoAway {
    timeLeft?: string;
}

// @public
export class LiveServerMessage {
    get data(): string | undefined;
    goAway?: LiveServerGoAway;
    serverContent?: LiveServerContent;
    sessionResumptionUpdate?: LiveServerSessionResumptionUpdate;
    setupComplete?: LiveServerSetupComplete;
    get text(): string | undefined;
    toolCall?: LiveServerToolCall;
    toolCallCancellation?: LiveServerToolCallCancellation;
    usageMetadata?: UsageMetadata;
}

// @public
export interface LiveServerSessionResumptionUpdate {
    lastConsumedClientMessageIndex?: string;
    newHandle?: string;
    resumable?: boolean;
}

// @public (undocumented)
export interface LiveServerSetupComplete {
    sessionId?: string;
}

// @public
export interface LiveServerToolCall {
    functionCalls?: FunctionCall[];
}

// @public
export interface LiveServerToolCallCancellation {
    ids?: string[];
}

// @public
export interface LogprobsResult {
    chosenCandidates?: LogprobsResultCandidate[];
    topCandidates?: LogprobsResultTopCandidates[];
}

// @public
export interface LogprobsResultCandidate {
    logProbability?: number;
    token?: string;
    tokenId?: number;
}

// @public
export interface LogprobsResultTopCandidates {
    candidates?: LogprobsResultCandidate[];
}

// @public
export interface MaskReferenceConfig {
    maskDilation?: number;
    maskMode?: MaskReferenceMode;
    segmentationClasses?: number[];
}

// @public
export class MaskReferenceImage {
    config?: MaskReferenceConfig;
    referenceId?: number;
    referenceImage?: Image_2;
    referenceType?: string;
    toReferenceImageAPI(): ReferenceImageAPIInternal;
}

// @public
export enum MaskReferenceMode {
    // (undocumented)
    MASK_MODE_BACKGROUND = "MASK_MODE_BACKGROUND",
    // (undocumented)
    MASK_MODE_DEFAULT = "MASK_MODE_DEFAULT",
    // (undocumented)
    MASK_MODE_FOREGROUND = "MASK_MODE_FOREGROUND",
    // (undocumented)
    MASK_MODE_SEMANTIC = "MASK_MODE_SEMANTIC",
    // (undocumented)
    MASK_MODE_USER_PROVIDED = "MASK_MODE_USER_PROVIDED"
}

// @public
export function mcpToTool(...args: [...Client[], CallableToolConfig | Client]): CallableTool;

// @public
export enum MediaModality {
    AUDIO = "AUDIO",
    DOCUMENT = "DOCUMENT",
    IMAGE = "IMAGE",
    MODALITY_UNSPECIFIED = "MODALITY_UNSPECIFIED",
    TEXT = "TEXT",
    VIDEO = "VIDEO"
}

// @public
export enum MediaResolution {
    MEDIA_RESOLUTION_HIGH = "MEDIA_RESOLUTION_HIGH",
    MEDIA_RESOLUTION_LOW = "MEDIA_RESOLUTION_LOW",
    MEDIA_RESOLUTION_MEDIUM = "MEDIA_RESOLUTION_MEDIUM",
    MEDIA_RESOLUTION_UNSPECIFIED = "MEDIA_RESOLUTION_UNSPECIFIED"
}

// @public
export enum Modality {
    AUDIO = "AUDIO",
    IMAGE = "IMAGE",
    MODALITY_UNSPECIFIED = "MODALITY_UNSPECIFIED",
    TEXT = "TEXT"
}

// @public
export interface ModalityTokenCount {
    modality?: MediaModality;
    tokenCount?: number;
}

// @public
export enum Mode {
    MODE_DYNAMIC = "MODE_DYNAMIC",
    MODE_UNSPECIFIED = "MODE_UNSPECIFIED"
}

// @public
export interface Model {
    checkpoints?: Checkpoint[];
    defaultCheckpointId?: string;
    description?: string;
    displayName?: string;
    endpoints?: Endpoint[];
    inputTokenLimit?: number;
    labels?: Record<string, string>;
    name?: string;
    outputTokenLimit?: number;
    supportedActions?: string[];
    tunedModelInfo?: TunedModelInfo;
    version?: string;
}

// @public (undocumented)
export class Models extends BaseModule {
    constructor(apiClient: ApiClient);
    computeTokens(params: types.ComputeTokensParameters): Promise<types.ComputeTokensResponse>;
    countTokens(params: types.CountTokensParameters): Promise<types.CountTokensResponse>;
    delete(params: types.DeleteModelParameters): Promise<types.DeleteModelResponse>;
    editImage: (params: types.EditImageParameters) => Promise<types.EditImageResponse>;
    embedContent(params: types.EmbedContentParameters): Promise<types.EmbedContentResponse>;
    generateContent: (params: types.GenerateContentParameters) => Promise<types.GenerateContentResponse>;
    generateContentStream: (params: types.GenerateContentParameters) => Promise<AsyncGenerator<types.GenerateContentResponse>>;
    generateImages: (params: types.GenerateImagesParameters) => Promise<types.GenerateImagesResponse>;
    generateVideos(params: types.GenerateVideosParameters): Promise<types.GenerateVideosOperation>;
    get(params: types.GetModelParameters): Promise<types.Model>;
    // (undocumented)
    list: (params?: types.ListModelsParameters) => Promise<Pager<types.Model>>;
    update(params: types.UpdateModelParameters): Promise<types.Model>;
    upscaleImage: (params: types.UpscaleImageParameters) => Promise<types.UpscaleImageResponse>;
}

// @public
export interface ModelSelectionConfig {
    featureSelectionPreference?: FeatureSelectionPreference;
}

// @public
export interface MultiSpeakerVoiceConfig {
    speakerVoiceConfigs?: SpeakerVoiceConfig[];
}

// @public
export interface Operation {
    done?: boolean;
    error?: Record<string, unknown>;
    metadata?: Record<string, unknown>;
    name?: string;
}

// @public
export interface OperationGetParameters {
    config?: GetOperationConfig;
    operation: GenerateVideosOperation;
}

// @public (undocumented)
export class Operations extends BaseModule {
    constructor(apiClient: ApiClient);
    getVideosOperation(parameters: types.OperationGetParameters): Promise<types.GenerateVideosOperation>;
}

// @public
export enum Outcome {
    OUTCOME_DEADLINE_EXCEEDED = "OUTCOME_DEADLINE_EXCEEDED",
    OUTCOME_FAILED = "OUTCOME_FAILED",
    OUTCOME_OK = "OUTCOME_OK",
    OUTCOME_UNSPECIFIED = "OUTCOME_UNSPECIFIED"
}

// @public
export enum PagedItem {
    // (undocumented)
    PAGED_ITEM_BATCH_JOBS = "batchJobs",
    // (undocumented)
    PAGED_ITEM_CACHED_CONTENTS = "cachedContents",
    // (undocumented)
    PAGED_ITEM_FILES = "files",
    // (undocumented)
    PAGED_ITEM_MODELS = "models",
    // (undocumented)
    PAGED_ITEM_TUNING_JOBS = "tuningJobs"
}

// @public
export class Pager<T> implements AsyncIterable<T> {
    [Symbol.asyncIterator](): AsyncIterator<T>;
    constructor(name: PagedItem, request: (params: PagedItemConfig) => Promise<PagedItemResponse<T>>, response: PagedItemResponse<T>, params: PagedItemConfig);
    getItem(index: number): T;
    hasNextPage(): boolean;
    // (undocumented)
    protected idxInternal: number;
    get name(): PagedItem;
    nextPage(): Promise<T[]>;
    get page(): T[];
    get pageLength(): number;
    get pageSize(): number;
    get params(): PagedItemConfig;
    // Warning: (ae-forgotten-export) The symbol "PagedItemConfig" needs to be exported by the entry point index.d.ts
    // Warning: (ae-forgotten-export) The symbol "PagedItemResponse" needs to be exported by the entry point index.d.ts
    //
    // (undocumented)
    protected requestInternal: (params: PagedItemConfig) => Promise<PagedItemResponse<T>>;
}

// @public
export interface Part {
    codeExecutionResult?: CodeExecutionResult;
    executableCode?: ExecutableCode;
    fileData?: FileData;
    functionCall?: FunctionCall;
    functionResponse?: FunctionResponse;
    inlineData?: Blob_2;
    text?: string;
    thought?: boolean;
    thoughtSignature?: string;
    videoMetadata?: VideoMetadata;
}

// @public (undocumented)
export type PartListUnion = PartUnion[] | PartUnion;

// @public
export interface PartnerModelTuningSpec {
    hyperParameters?: Record<string, unknown>;
    trainingDatasetUri?: string;
    validationDatasetUri?: string;
}

// @public (undocumented)
export type PartUnion = Part | string;

// @public
export enum PersonGeneration {
    ALLOW_ADULT = "ALLOW_ADULT",
    ALLOW_ALL = "ALLOW_ALL",
    DONT_ALLOW = "DONT_ALLOW"
}

// @public
export interface PrebuiltVoiceConfig {
    voiceName?: string;
}

// @public
export interface ProactivityConfig {
    proactiveAudio?: boolean;
}

// @public
export interface RagChunk {
    pageSpan?: RagChunkPageSpan;
    text?: string;
}

// @public
export interface RagChunkPageSpan {
    firstPage?: number;
    lastPage?: number;
}

// @public
export interface RagRetrievalConfig {
    filter?: RagRetrievalConfigFilter;
    hybridSearch?: RagRetrievalConfigHybridSearch;
    ranking?: RagRetrievalConfigRanking;
    topK?: number;
}

// @public
export interface RagRetrievalConfigFilter {
    metadataFilter?: string;
    vectorDistanceThreshold?: number;
    vectorSimilarityThreshold?: number;
}

// @public
export interface RagRetrievalConfigHybridSearch {
    alpha?: number;
}

// @public
export interface RagRetrievalConfigRanking {
    llmRanker?: RagRetrievalConfigRankingLlmRanker;
    rankService?: RagRetrievalConfigRankingRankService;
}

// @public
export interface RagRetrievalConfigRankingLlmRanker {
    modelName?: string;
}

// @public
export interface RagRetrievalConfigRankingRankService {
    modelName?: string;
}

// @public
export class RawReferenceImage {
    referenceId?: number;
    referenceImage?: Image_2;
    referenceType?: string;
    toReferenceImageAPI(): ReferenceImageAPIInternal;
}

// @public
export interface RealtimeInputConfig {
    activityHandling?: ActivityHandling;
    automaticActivityDetection?: AutomaticActivityDetection;
    turnCoverage?: TurnCoverage;
}

// @public (undocumented)
export type ReferenceImage = RawReferenceImage | MaskReferenceImage | ControlReferenceImage | StyleReferenceImage | SubjectReferenceImage;

// @public
export interface ReplayFile {
    // (undocumented)
    interactions?: ReplayInteraction[];
    // (undocumented)
    replayId?: string;
}

// @public
export interface ReplayInteraction {
    // (undocumented)
    request?: ReplayRequest;
    // (undocumented)
    response?: ReplayResponse;
}

// @public
export interface ReplayRequest {
    // (undocumented)
    bodySegments?: Record<string, unknown>[];
    // (undocumented)
    headers?: Record<string, string>;
    // (undocumented)
    method?: string;
    // (undocumented)
    url?: string;
}

// @public
export class ReplayResponse {
    // (undocumented)
    bodySegments?: Record<string, unknown>[];
    // (undocumented)
    headers?: Record<string, string>;
    // (undocumented)
    sdkResponseSegments?: Record<string, unknown>[];
    // (undocumented)
    statusCode?: number;
}

// @public
export interface Retrieval {
    disableAttribution?: boolean;
    externalApi?: ExternalApi;
    vertexAiSearch?: VertexAISearch;
    vertexRagStore?: VertexRagStore;
}

// @public
export interface RetrievalConfig {
    languageCode?: string;
    latLng?: LatLng;
}

// @public
export interface RetrievalMetadata {
    googleSearchDynamicRetrievalScore?: number;
}

// @public
export interface SafetyAttributes {
    categories?: string[];
    contentType?: string;
    scores?: number[];
}

// @public
export enum SafetyFilterLevel {
    // (undocumented)
    BLOCK_LOW_AND_ABOVE = "BLOCK_LOW_AND_ABOVE",
    // (undocumented)
    BLOCK_MEDIUM_AND_ABOVE = "BLOCK_MEDIUM_AND_ABOVE",
    // (undocumented)
    BLOCK_NONE = "BLOCK_NONE",
    // (undocumented)
    BLOCK_ONLY_HIGH = "BLOCK_ONLY_HIGH"
}

// @public
export interface SafetyRating {
    blocked?: boolean;
    category?: HarmCategory;
    overwrittenThreshold?: HarmBlockThreshold;
    probability?: HarmProbability;
    probabilityScore?: number;
    severity?: HarmSeverity;
    severityScore?: number;
}

// @public
export interface SafetySetting {
    category?: HarmCategory;
    method?: HarmBlockMethod;
    threshold?: HarmBlockThreshold;
}

// @public
export enum Scale {
    A_FLAT_MAJOR_F_MINOR = "A_FLAT_MAJOR_F_MINOR",
    A_MAJOR_G_FLAT_MINOR = "A_MAJOR_G_FLAT_MINOR",
    B_FLAT_MAJOR_G_MINOR = "B_FLAT_MAJOR_G_MINOR",
    B_MAJOR_A_FLAT_MINOR = "B_MAJOR_A_FLAT_MINOR",
    C_MAJOR_A_MINOR = "C_MAJOR_A_MINOR",
    D_FLAT_MAJOR_B_FLAT_MINOR = "D_FLAT_MAJOR_B_FLAT_MINOR",
    D_MAJOR_B_MINOR = "D_MAJOR_B_MINOR",
    E_FLAT_MAJOR_C_MINOR = "E_FLAT_MAJOR_C_MINOR",
    E_MAJOR_D_FLAT_MINOR = "E_MAJOR_D_FLAT_MINOR",
    F_MAJOR_D_MINOR = "F_MAJOR_D_MINOR",
    G_FLAT_MAJOR_E_FLAT_MINOR = "G_FLAT_MAJOR_E_FLAT_MINOR",
    G_MAJOR_E_MINOR = "G_MAJOR_E_MINOR",
    SCALE_UNSPECIFIED = "SCALE_UNSPECIFIED"
}

// @public
export interface Schema {
    anyOf?: Schema[];
    default?: unknown;
    description?: string;
    enum?: string[];
    example?: unknown;
    format?: string;
    items?: Schema;
    maximum?: number;
    maxItems?: string;
    maxLength?: string;
    maxProperties?: string;
    minimum?: number;
    minItems?: string;
    minLength?: string;
    minProperties?: string;
    nullable?: boolean;
    pattern?: string;
    properties?: Record<string, Schema>;
    propertyOrdering?: string[];
    required?: string[];
    title?: string;
    type?: Type;
}

// @public (undocumented)
export type SchemaUnion = Schema | unknown;

// @public
export interface SearchEntryPoint {
    renderedContent?: string;
    sdkBlob?: string;
}

// @public
export interface Segment {
    endIndex?: number;
    partIndex?: number;
    startIndex?: number;
    text?: string;
}

// @public
export interface SendMessageParameters {
    config?: GenerateContentConfig;
    message: PartListUnion;
}

// @public
export class Session {
    constructor(conn: WebSocket_2, apiClient: ApiClient);
    close(): void;
    // (undocumented)
    readonly conn: WebSocket_2;
    sendClientContent(params: types.LiveSendClientContentParameters): void;
    sendRealtimeInput(params: types.LiveSendRealtimeInputParameters): void;
    sendToolResponse(params: types.LiveSendToolResponseParameters): void;
}

// @public
export interface SessionResumptionConfig {
    handle?: string;
    transparent?: boolean;
}

// @public
export function setDefaultBaseUrls(baseUrlParams: BaseUrlParameters): void;

// @public
export interface SlidingWindow {
    targetTokens?: string;
}

// @public
export interface SpeakerVoiceConfig {
    speaker?: string;
    voiceConfig?: VoiceConfig;
}

// @public
export interface SpeechConfig {
    languageCode?: string;
    multiSpeakerVoiceConfig?: MultiSpeakerVoiceConfig;
    voiceConfig?: VoiceConfig;
}

// @public (undocumented)
export type SpeechConfigUnion = SpeechConfig | string;

// @public
export enum StartSensitivity {
    START_SENSITIVITY_HIGH = "START_SENSITIVITY_HIGH",
    START_SENSITIVITY_LOW = "START_SENSITIVITY_LOW",
    START_SENSITIVITY_UNSPECIFIED = "START_SENSITIVITY_UNSPECIFIED"
}

// @public
export interface StyleReferenceConfig {
    styleDescription?: string;
}

// @public
export class StyleReferenceImage {
    config?: StyleReferenceConfig;
    referenceId?: number;
    referenceImage?: Image_2;
    referenceType?: string;
    toReferenceImageAPI(): ReferenceImageAPIInternal;
}

// @public
export interface SubjectReferenceConfig {
    subjectDescription?: string;
    subjectType?: SubjectReferenceType;
}

// @public
export class SubjectReferenceImage {
    config?: SubjectReferenceConfig;
    referenceId?: number;
    referenceImage?: Image_2;
    referenceType?: string;
    // (undocumented)
    toReferenceImageAPI(): ReferenceImageAPIInternal;
}

// @public
export enum SubjectReferenceType {
    // (undocumented)
    SUBJECT_TYPE_ANIMAL = "SUBJECT_TYPE_ANIMAL",
    // (undocumented)
    SUBJECT_TYPE_DEFAULT = "SUBJECT_TYPE_DEFAULT",
    // (undocumented)
    SUBJECT_TYPE_PERSON = "SUBJECT_TYPE_PERSON",
    // (undocumented)
    SUBJECT_TYPE_PRODUCT = "SUBJECT_TYPE_PRODUCT"
}

// @public
export interface SupervisedHyperParameters {
    adapterSize?: AdapterSize;
    epochCount?: string;
    learningRateMultiplier?: number;
}

// @public
export interface SupervisedTuningDatasetDistribution {
    billableSum?: string;
    buckets?: SupervisedTuningDatasetDistributionDatasetBucket[];
    max?: number;
    mean?: number;
    median?: number;
    min?: number;
    p5?: number;
    p95?: number;
    sum?: string;
}

// @public
export interface SupervisedTuningDatasetDistributionDatasetBucket {
    count?: number;
    left?: number;
    right?: number;
}

// @public
export interface SupervisedTuningDataStats {
    droppedExampleReasons?: string[];
    totalBillableCharacterCount?: string;
    totalBillableTokenCount?: string;
    totalTruncatedExampleCount?: string;
    totalTuningCharacterCount?: string;
    truncatedExampleIndices?: string[];
    tuningDatasetExampleCount?: string;
    tuningStepCount?: string;
    userDatasetExamples?: Content[];
    userInputTokenDistribution?: SupervisedTuningDatasetDistribution;
    userMessagePerExampleDistribution?: SupervisedTuningDatasetDistribution;
    userOutputTokenDistribution?: SupervisedTuningDatasetDistribution;
}

// @public
export interface SupervisedTuningSpec {
    exportLastCheckpointOnly?: boolean;
    hyperParameters?: SupervisedHyperParameters;
    trainingDatasetUri?: string;
    validationDatasetUri?: string;
}

// @public (undocumented)
export interface TestTableFile {
    // (undocumented)
    comment?: string;
    // (undocumented)
    parameterNames?: string[];
    // (undocumented)
    testMethod?: string;
    // (undocumented)
    testTable?: TestTableItem[];
}

// @public (undocumented)
export interface TestTableItem {
    exceptionIfMldev?: string;
    exceptionIfVertex?: string;
    hasUnion?: boolean;
    ignoreKeys?: string[];
    name?: string;
    overrideReplayId?: string;
    parameters?: Record<string, unknown>;
    skipInApiMode?: string;
}

// @public
export interface ThinkingConfig {
    includeThoughts?: boolean;
    thinkingBudget?: number;
}

// @public (undocumented)
export class Tokens extends BaseModule {
    constructor(apiClient: ApiClient);
    create(params: types.CreateAuthTokenParameters): Promise<types.AuthToken>;
}

// @public
export interface TokensInfo {
    role?: string;
    tokenIds?: string[];
    tokens?: string[];
}

// @public
export interface Tool {
    codeExecution?: ToolCodeExecution;
    computerUse?: ToolComputerUse;
    enterpriseWebSearch?: EnterpriseWebSearch;
    functionDeclarations?: FunctionDeclaration[];
    googleMaps?: GoogleMaps;
    googleSearch?: GoogleSearch;
    googleSearchRetrieval?: GoogleSearchRetrieval;
    retrieval?: Retrieval;
    urlContext?: UrlContext;
}

// @public
export interface ToolCodeExecution {
}

// @public
export interface ToolComputerUse {
    environment?: Environment;
}

// @public
export interface ToolConfig {
    functionCallingConfig?: FunctionCallingConfig;
    retrievalConfig?: RetrievalConfig;
}

// @public (undocumented)
export type ToolListUnion = ToolUnion[];

// @public (undocumented)
export type ToolUnion = Tool | CallableTool;

// @public
export enum TrafficType {
    ON_DEMAND = "ON_DEMAND",
    PROVISIONED_THROUGHPUT = "PROVISIONED_THROUGHPUT",
    TRAFFIC_TYPE_UNSPECIFIED = "TRAFFIC_TYPE_UNSPECIFIED"
}

// @public
export interface Transcription {
    finished?: boolean;
    text?: string;
}

// @public (undocumented)
export interface TunedModel {
    checkpoints?: TunedModelCheckpoint[];
    endpoint?: string;
    model?: string;
}

// @public
export interface TunedModelCheckpoint {
    checkpointId?: string;
    endpoint?: string;
    epoch?: string;
    step?: string;
}

// @public
export interface TunedModelInfo {
    baseModel?: string;
    createTime?: string;
    updateTime?: string;
}

// @public
export interface TuningDataset {
    examples?: TuningExample[];
    gcsUri?: string;
    vertexDatasetResource?: string;
}

// @public
export interface TuningDataStats {
    distillationDataStats?: DistillationDataStats;
    supervisedTuningDataStats?: SupervisedTuningDataStats;
}

// @public (undocumented)
export interface TuningExample {
    output?: string;
    textInput?: string;
}

// @public
export interface TuningJob {
    baseModel?: string;
    createTime?: string;
    description?: string;
    distillationSpec?: DistillationSpec;
    encryptionSpec?: EncryptionSpec;
    endTime?: string;
    error?: GoogleRpcStatus;
    experiment?: string;
    labels?: Record<string, string>;
    name?: string;
    partnerModelTuningSpec?: PartnerModelTuningSpec;
    pipelineJob?: string;
    satisfiesPzi?: boolean;
    satisfiesPzs?: boolean;
    serviceAccount?: string;
    startTime?: string;
    state?: JobState;
    supervisedTuningSpec?: SupervisedTuningSpec;
    tunedModel?: TunedModel;
    tunedModelDisplayName?: string;
    tuningDataStats?: TuningDataStats;
    updateTime?: string;
}

// @public (undocumented)
export interface TuningValidationDataset {
    gcsUri?: string;
    vertexDatasetResource?: string;
}

// @public
export enum TurnCoverage {
    TURN_COVERAGE_UNSPECIFIED = "TURN_COVERAGE_UNSPECIFIED",
    TURN_INCLUDES_ALL_INPUT = "TURN_INCLUDES_ALL_INPUT",
    TURN_INCLUDES_ONLY_ACTIVITY = "TURN_INCLUDES_ONLY_ACTIVITY"
}

// @public
export enum Type {
    ARRAY = "ARRAY",
    BOOLEAN = "BOOLEAN",
    INTEGER = "INTEGER",
    NULL = "NULL",
    NUMBER = "NUMBER",
    OBJECT = "OBJECT",
    STRING = "STRING",
    TYPE_UNSPECIFIED = "TYPE_UNSPECIFIED"
}

// @public
export interface UpdateCachedContentConfig {
    abortSignal?: AbortSignal;
    expireTime?: string;
    httpOptions?: HttpOptions;
    ttl?: string;
}

// @public (undocumented)
export interface UpdateCachedContentParameters {
    config?: UpdateCachedContentConfig;
    name: string;
}

// @public
export interface UpdateModelConfig {
    abortSignal?: AbortSignal;
    // (undocumented)
    defaultCheckpointId?: string;
    // (undocumented)
    description?: string;
    // (undocumented)
    displayName?: string;
    httpOptions?: HttpOptions;
}

// @public
export interface UpdateModelParameters {
    // (undocumented)
    config?: UpdateModelConfig;
    // (undocumented)
    model: string;
}

// @public
export interface UploadFileConfig {
    abortSignal?: AbortSignal;
    displayName?: string;
    httpOptions?: HttpOptions;
    mimeType?: string;
    name?: string;
}

// @public
export interface UploadFileParameters {
    config?: UploadFileConfig;
    file: string | globalThis.Blob;
}

// @public
export interface UpscaleImageConfig {
    abortSignal?: AbortSignal;
    enhanceInputImage?: boolean;
    httpOptions?: HttpOptions;
    imagePreservationFactor?: number;
    includeRaiReason?: boolean;
    outputCompressionQuality?: number;
    outputMimeType?: string;
}

// @public
export interface UpscaleImageParameters {
    config?: UpscaleImageConfig;
    image: Image_2;
    model: string;
    upscaleFactor: string;
}

// @public (undocumented)
export class UpscaleImageResponse {
    generatedImages?: GeneratedImage[];
}

// @public
export interface UrlContext {
}

// @public
export interface UrlContextMetadata {
    urlMetadata?: UrlMetadata[];
}

// @public
export interface UrlMetadata {
    retrievedUrl?: string;
    urlRetrievalStatus?: UrlRetrievalStatus;
}

// @public
export enum UrlRetrievalStatus {
    URL_RETRIEVAL_STATUS_ERROR = "URL_RETRIEVAL_STATUS_ERROR",
    URL_RETRIEVAL_STATUS_SUCCESS = "URL_RETRIEVAL_STATUS_SUCCESS",
    URL_RETRIEVAL_STATUS_UNSPECIFIED = "URL_RETRIEVAL_STATUS_UNSPECIFIED"
}

// @public
export interface UsageMetadata {
    cachedContentTokenCount?: number;
    cacheTokensDetails?: ModalityTokenCount[];
    promptTokenCount?: number;
    promptTokensDetails?: ModalityTokenCount[];
    responseTokenCount?: number;
    responseTokensDetails?: ModalityTokenCount[];
    thoughtsTokenCount?: number;
    toolUsePromptTokenCount?: number;
    toolUsePromptTokensDetails?: ModalityTokenCount[];
    totalTokenCount?: number;
    trafficType?: TrafficType;
}

// @public
export interface VertexAISearch {
    datastore?: string;
    dataStoreSpecs?: VertexAISearchDataStoreSpec[];
    engine?: string;
    filter?: string;
    maxResults?: number;
}

// @public
export interface VertexAISearchDataStoreSpec {
    dataStore?: string;
    filter?: string;
}

// @public
export interface VertexRagStore {
    ragCorpora?: string[];
    ragResources?: VertexRagStoreRagResource[];
    ragRetrievalConfig?: RagRetrievalConfig;
    similarityTopK?: number;
    storeContext?: boolean;
    vectorDistanceThreshold?: number;
}

// @public
export interface VertexRagStoreRagResource {
    ragCorpus?: string;
    ragFileIds?: string[];
}

// @public
export interface Video {
    mimeType?: string;
    uri?: string;
    videoBytes?: string;
}

// @public
export enum VideoCompressionQuality {
    LOSSLESS = "LOSSLESS",
    OPTIMIZED = "OPTIMIZED"
}

// @public
export interface VideoMetadata {
    endOffset?: string;
    fps?: number;
    startOffset?: string;
}

// @public
export interface VoiceConfig {
    prebuiltVoiceConfig?: PrebuiltVoiceConfig;
}

// @public
export interface WeightedPrompt {
    text?: string;
    weight?: number;
}

// (No @packageDocumentation comment for this package)

```
