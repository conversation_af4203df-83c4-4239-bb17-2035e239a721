name: Test packaging

on:
  push:
    branches: [ 'main' ]
  pull_request:
    branches: [ 'main' ]

jobs:
  test-pack:

    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [22.x]

    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: |
          package-lock.json
          sdk-samples/package-lock.json
    - run: npm ci
    - run: test/packaging/test_packaging.sh
