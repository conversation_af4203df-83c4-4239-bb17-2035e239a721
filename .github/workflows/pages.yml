name: Update pages

on:
  push:
    branches: [ 'main' ]
  release:
    types: [ 'published', 'unpublished' ]
concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  update-pages:
    runs-on: ubuntu-latest
    permissions:
      contents: write

    steps:
      - name: Checkout main branch
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.x'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Generate pages
        id: generate_pages
        run: |
          GITHUB_EVENT="${{ github.event_name }}"
          echo "Generating pages from $GITHUB_EVENT"
          if [[ "$GITHUB_EVENT" == 'release' ]]; then
            echo "is_release_event=true" >> $GITHUB_OUTPUT
            npm run pages-release
          else
            echo "is_release_event=false" >> $GITHUB_OUTPUT
            npm run pages-main
          fi
      - name: Check for documentation changes
        id: check_changes
        run: |
          if [[ $(git diff --shortstat origin/pages-only -- main_docs/ release_docs/) ]]; then
            echo "Documentation changes detected."
            echo "has_changes=true" >> $GITHUB_OUTPUT
          else
            echo "No documentation changes detected."
            echo "has_changes=false" >> $GITHUB_OUTPUT
          fi
      - name: Checkout target branch (pages-only) in a sub-directory
        if: steps.check_changes.outputs.has_changes == 'true'
        uses: actions/checkout@v4
        with:
          ref: pages-only
          path: pages-deploy
      - name: Replace docs
        if: steps.check_changes.outputs.has_changes == 'true'
        env:
          IS_RELEASE_EVENT: ${{ steps.generate_pages.outputs.is_release_event }}
        run:  |
          if [[ "$IS_RELEASE_EVENT" == 'true' ]]; then
            echo "Updating release_docs"
            rm -rf ./pages-deploy/release_docs || true
            cp -a ./release_docs/. ./pages-deploy/release_docs/
          else
            echo "Updating main_docs"
            rm -rf ./pages-deploy/main_docs || true
            cp -a ./main_docs/. ./pages-deploy/main_docs/
          fi
      - name: Commit and push changes to pages-only branch
        if: steps.check_changes.outputs.has_changes == 'true'
        env:
          IS_RELEASE_EVENT: ${{ steps.generate_pages.outputs.is_release_event }}
        working-directory: ./pages-deploy
        run: |
          git config user.email "<EMAIL>"
          git config user.name "genai-sdk-bot"

          if [[ -z $(git status --porcelain) ]]; then
             echo "No effective changes staged after copy. Skipping commit."
             exit 0
          fi

          if [[ "$IS_RELEASE_EVENT" == 'true' ]]; then
            echo "Staging changes in release_docs/..."
            git add release_docs/
            COMMIT_MESSAGE="Update release pages from release-please commit: ${{ github.sha }}"
          else
            echo "Staging changes in main_docs/..."
            git add main_docs/
            COMMIT_MESSAGE="Update main pages from main branch commit: ${{ github.sha }}"
          fi

          if git diff --staged --quiet; then
            echo "No relevant changes detected to commit."
            exit 0
          fi
          echo "Committing message: $COMMIT_MESSAGE"
          git commit -m "$COMMIT_MESSAGE"
          git push origin pages-only