b4d6e60a9b97e7b98c63df9308728c5c88c0b40c398046772c63447b94608b4d
Server Address: generativelanguage.googleapis.com
Port: 443
Protocol: https
********************************************************************************
POST /v1beta/models/gemini-2.0-flash:streamGenerateContent?alt=sse HTTP/1.1
Accept: */*
Accept-Encoding: gzip, deflate
Accept-Language: *
Connection: keep-alive
Content-Length: 824
Content-Type: application/json
Sec-Fetch-Mode: cors


{"contents":[{"parts":[{"text":"Divide 10 by 2 using the customDivide function"}],"role":"user"},{"parts":[{"functionCall":{"name":"customDivide","args":{"denominator":2,"numerator":10}}}],"role":"model"},{"parts":[{"functionResponse":{"name":"customDivide","response":{"result":42}}}],"role":"user"},{"parts":[{"text":"I"}],"role":"model"},{"parts":[{"text":" have divided 10 by 2 using the customDivide function. The result is "}],"role":"model"},{"parts":[{"text":"42.\n"}],"role":"model"},{"parts":[{"text":"Thanks!"}],"role":"user"}],"tools":[{"functionDeclarations":[{"description":"Custom divide function","name":"customDivide","parameters":{"type":"OBJECT","properties":{"numerator":{"type":"NUMBER"},"denominator":{"type":"NUMBER"}}}}]}],"toolConfig":{"functionCallingConfig":{"mode":"AUTO"}},"generationConfig":{}}