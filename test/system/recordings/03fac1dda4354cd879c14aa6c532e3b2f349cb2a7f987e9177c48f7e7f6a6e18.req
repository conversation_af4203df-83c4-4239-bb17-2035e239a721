b4d6e60a9b97e7b98c63df9308728c5c88c0b40c398046772c63447b94608b4d
Server Address: us-central1-aiplatform.googleapis.com
Port: 443
Protocol: https
********************************************************************************
POST /v1beta1/projects/REDACTED/locations/REDACTED/publishers/google/models/gemini-2.0-flash:streamGenerateContent?alt=sse HTTP/1.1
Accept: */*
Accept-Encoding: gzip, deflate
Accept-Language: *
Connection: keep-alive
Content-Length: 589
Content-Type: application/json
Sec-Fetch-Mode: cors


{"contents":[{"parts":[{"text":"Divide 10 by 2 using the customDivide function"}],"role":"user"},{"parts":[{"functionCall":{"name":"customDivide","args":{"numerator":10,"denominator":2}}}],"role":"model"},{"parts":[{"functionResponse":{"name":"customDivide","response":{"result":null}}}],"role":"user"}],"tools":[{"functionDeclarations":[{"description":"Custom divide function","name":"customDivide","parameters":{"type":"OBJECT","properties":{"numerator":{"type":"NUMBER"},"denominator":{"type":"NUMBER"}}}}]}],"toolConfig":{"functionCallingConfig":{"mode":"AUTO"}},"generationConfig":{}}