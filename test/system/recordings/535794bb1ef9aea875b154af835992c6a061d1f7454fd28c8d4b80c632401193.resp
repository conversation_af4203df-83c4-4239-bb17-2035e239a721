Status code: 200 
Date: Wed, 14 May 2025 18:12:50 GMT
Content-Length: 725
X-Content-Type-Options: nosniff
Content-Type: application/json; charset=UTF-8
Vary: Origin
Vary: X-Origin
Vary: Referer
Content-Encoding: gzip
Server: scaffolding on HTTPServer2
X-Xss-Protection: 0
X-Frame-Options: SAMEORIGIN
X-Goog-Sherlog-Link: https://sherlog.corp.google.com/#workflow?config=Serving&dataid=aiplatform.googleapis.com/google.cloud.aiplatform.v1beta1.PredictionService.GenerateContent&gaia=************&reason=sharedLink&version=183f7750de9ae60a8e08f28f6a695cac

{
  "candidates": [
    {
      "content": {
        "role": "model",
        "parts": [
          {
            "text": "The old lighthouse keeper, <PERSON>, squinted at the storm-tossed sea. For fifty years, he’d kept the beacon burning, a steadfast guide in the darkness. Tonight, the wind howled like a banshee, threatening to extinguish the flame. He fought the gale, securing the lamp, his weathered hands trembling. Suddenly, a faint SOS crackled over the radio. A small fishing boat, lost and battered. <PERSON> adjusted the beam, focusing its unwavering light on the struggling vessel. Hours later, silence. The storm subsided. In the morning, a grateful fisherman arrived, carrying a single, perfect rose.\n"
          }
        ]
      },
      "finishReason": "STOP",
      "avgLogprobs": -0.59970861720287894
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 11,
    "candidatesTokenCount": 127,
    "totalTokenCount": 138,
    "trafficType": "ON_DEMAND",
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 11
      }
    ],
    "candidatesTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 127
      }
    ]
  },
  "modelVersion": "gemini-2.0-flash",
  "createTime": "2025-05-14T18:12:49.691266Z",
  "responseId": "Id0kaMKYKviKmecPs63QmAk"
}
