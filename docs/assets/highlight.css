:root {
    --light-hl-0: #795E26;
    --dark-hl-0: #DCDCAA;
    --light-hl-1: #000000;
    --dark-hl-1: #D4D4D4;
    --light-hl-2: #A31515;
    --dark-hl-2: #CE9178;
    --light-hl-3: #AF00DB;
    --dark-hl-3: #C586C0;
    --light-hl-4: #001080;
    --dark-hl-4: #9CDCFE;
    --light-hl-5: #0000FF;
    --dark-hl-5: #569CD6;
    --light-hl-6: #0070C1;
    --dark-hl-6: #4FC1FF;
    --light-hl-7: #800000;
    --dark-hl-7: #808080;
    --light-hl-8: #800000;
    --dark-hl-8: #569CD6;
    --light-hl-9: #E50000;
    --dark-hl-9: #9CDCFE;
    --light-hl-10: #0000FF;
    --dark-hl-10: #CE9178;
    --light-hl-11: #000000FF;
    --dark-hl-11: #D4D4D4;
    --light-hl-12: #267F99;
    --dark-hl-12: #4EC9B0;
    --light-hl-13: #008000;
    --dark-hl-13: #6A9955;
    --light-hl-14: #098658;
    --dark-hl-14: #B5CEA8;
    --light-hl-15: #EE0000;
    --dark-hl-15: #D7BA7D;
    --light-code-background: #FFFFFF;
    --dark-code-background: #1E1E1E;
}

@media (prefers-color-scheme: light) { :root {
    --hl-0: var(--light-hl-0);
    --hl-1: var(--light-hl-1);
    --hl-2: var(--light-hl-2);
    --hl-3: var(--light-hl-3);
    --hl-4: var(--light-hl-4);
    --hl-5: var(--light-hl-5);
    --hl-6: var(--light-hl-6);
    --hl-7: var(--light-hl-7);
    --hl-8: var(--light-hl-8);
    --hl-9: var(--light-hl-9);
    --hl-10: var(--light-hl-10);
    --hl-11: var(--light-hl-11);
    --hl-12: var(--light-hl-12);
    --hl-13: var(--light-hl-13);
    --hl-14: var(--light-hl-14);
    --hl-15: var(--light-hl-15);
    --code-background: var(--light-code-background);
} }

@media (prefers-color-scheme: dark) { :root {
    --hl-0: var(--dark-hl-0);
    --hl-1: var(--dark-hl-1);
    --hl-2: var(--dark-hl-2);
    --hl-3: var(--dark-hl-3);
    --hl-4: var(--dark-hl-4);
    --hl-5: var(--dark-hl-5);
    --hl-6: var(--dark-hl-6);
    --hl-7: var(--dark-hl-7);
    --hl-8: var(--dark-hl-8);
    --hl-9: var(--dark-hl-9);
    --hl-10: var(--dark-hl-10);
    --hl-11: var(--dark-hl-11);
    --hl-12: var(--dark-hl-12);
    --hl-13: var(--dark-hl-13);
    --hl-14: var(--dark-hl-14);
    --hl-15: var(--dark-hl-15);
    --code-background: var(--dark-code-background);
} }

:root[data-theme='light'] {
    --hl-0: var(--light-hl-0);
    --hl-1: var(--light-hl-1);
    --hl-2: var(--light-hl-2);
    --hl-3: var(--light-hl-3);
    --hl-4: var(--light-hl-4);
    --hl-5: var(--light-hl-5);
    --hl-6: var(--light-hl-6);
    --hl-7: var(--light-hl-7);
    --hl-8: var(--light-hl-8);
    --hl-9: var(--light-hl-9);
    --hl-10: var(--light-hl-10);
    --hl-11: var(--light-hl-11);
    --hl-12: var(--light-hl-12);
    --hl-13: var(--light-hl-13);
    --hl-14: var(--light-hl-14);
    --hl-15: var(--light-hl-15);
    --code-background: var(--light-code-background);
}

:root[data-theme='dark'] {
    --hl-0: var(--dark-hl-0);
    --hl-1: var(--dark-hl-1);
    --hl-2: var(--dark-hl-2);
    --hl-3: var(--dark-hl-3);
    --hl-4: var(--dark-hl-4);
    --hl-5: var(--dark-hl-5);
    --hl-6: var(--dark-hl-6);
    --hl-7: var(--dark-hl-7);
    --hl-8: var(--dark-hl-8);
    --hl-9: var(--dark-hl-9);
    --hl-10: var(--dark-hl-10);
    --hl-11: var(--dark-hl-11);
    --hl-12: var(--dark-hl-12);
    --hl-13: var(--dark-hl-13);
    --hl-14: var(--dark-hl-14);
    --hl-15: var(--dark-hl-15);
    --code-background: var(--dark-code-background);
}

.hl-0 { color: var(--hl-0); }
.hl-1 { color: var(--hl-1); }
.hl-2 { color: var(--hl-2); }
.hl-3 { color: var(--hl-3); }
.hl-4 { color: var(--hl-4); }
.hl-5 { color: var(--hl-5); }
.hl-6 { color: var(--hl-6); }
.hl-7 { color: var(--hl-7); }
.hl-8 { color: var(--hl-8); }
.hl-9 { color: var(--hl-9); }
.hl-10 { color: var(--hl-10); }
.hl-11 { color: var(--hl-11); }
.hl-12 { color: var(--hl-12); }
.hl-13 { color: var(--hl-13); }
.hl-14 { color: var(--hl-14); }
.hl-15 { color: var(--hl-15); }
pre, code { background: var(--code-background); }
