b4d6e60a9b97e7b98c63df9308728c5c88c0b40c398046772c63447b94608b4d
Server Address: us-central1-aiplatform.googleapis.com
Port: 443
Protocol: https
********************************************************************************
POST /v1beta1/projects/REDACTED/locations/REDACTED/publishers/google/models/gemini-2.5-flash:generateContent HTTP/1.1
Accept: */*
Accept-Encoding: gzip, deflate
Accept-Language: *
Connection: keep-alive
Content-Length: 487
Content-Type: application/json
Sec-Fetch-Mode: cors


{"contents":[{"parts":[{"text":"call the greeter once with name: jone smith, and greeting: Hello"}],"role":"user"}],"tools":[{"functionDeclarations":[{"name":"greet","parametersJsonSchema":{"type":"object","properties":{"name":{"type":["string","number","null"]},"greeting":{"type":"string"}},"required":["name","greeting"],"additionalProperties":false,"$schema":"http://json-schema.org/draft-07/schema#"}}]}],"toolConfig":{"functionCallingConfig":{"mode":"AUTO"}},"generationConfig":{}}